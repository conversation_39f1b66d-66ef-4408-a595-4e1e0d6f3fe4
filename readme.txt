=== WP Job Openings - Job Listing, Career Page and Recruitment Plugin ===
Contributors: a<PERSON><PERSON>, ara<PERSON><PERSON><PERSON>h, anant<PERSON><PERSON><PERSON><PERSON>, sarathar, adhun, nithi22
Tags: jobs, job listing, job openings, job board, careers page, jobs page, wp job opening, jobs plugin
Requires at least: 4.8
Tested up to: 6.8
Requires PHP: 5.6
Stable tag: trunk
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Donate link: https://www.buymeacoffee.com/awsm

== Summary ==

Super simple Job Listing plugin to manage Job Openings and Applicants on your WordPress site.

== Description ==
**WP Job Openings plugin is the most simple yet powerful plugin for setting up a job listing page for your WordPress website.**

WP Job Openings is designed after carefully analyzing hundreds of job listing layouts and methods. The plugin is super simple to use and extensible to a high-performing recruitment tool.

https://www.youtube.com/watch?v=xqAlRljVKJ0

The plugin comes with two layouts - Grid and List which are designed carefully according to the modern design and User Experience principles. The highlight of the plugin is its totally flexible filter options.


**[View Demo](https://demo.wpjobopenings.com/)**

**[Visit website - wpjobopenings.com](https://wpjobopenings.com/)**


= Key Features =

* Super Simple and Easy to Set Up and Use
* Two Different Modern Layouts
* Clean and User-Friendly Designs
* Unlimited Job Specifications
* Unlimited Filtering Options
* Search Option to find jobs
* AJAX Powered Job Listing and Filtering
* Comes with Default Form to Submit Applications
* HR Role for setting up HR user
* Options to Customise Email Notifications
* Custom Email Notification Templates
* Application Listings in Plugin
* Job Expiry Options
* Job posting structured data for better SEO
* Recent Jobs Widget
* WPML Support
* Developer Friendly (Lots of hooks!)
* Detailed Documentation
* Tested with more than 50 top WordPress themes and Plugins


= Add-ons =

* [Docs Viewer](https://wordpress.org/plugins/docs-viewer-add-on-for-wp-job-openings/) (FREE)
* [Auto-Delete Applications for GDPR Compliance](https://wordpress.org/plugins/auto-delete-applications-add-on-for-wp-job-openings/) (FREE)
* [PRO Pack](https://wpjobopenings.com/pro-pack/) (PREMIUM)
* [User Access Control Add-on](https://wpjobopenings.com/add-ons/user-access-control/) (PREMIUM)
* [Job Alerts Add-on](https://wpjobopenings.com/add-ons/job-alerts/) (PREMIUM)

= WP Job Openings PRO Features =

**Power up your job listing with the PRO pack Add-on**

* Form Builder - Make your own application form
* Shortlist, Reject and Select Applicants
* Rate and Filter Applications
* Custom Email Notifications & Templates
* Email CC option for job submission notifications
* Notes and Activity Log
* Option to Filter and Export Applications
* Attach uploaded file with email notifications
* Shortcode generator for generating customised job lists
* Use third-party forms and custom application URLs

**[Get PRO Pack](https://wpjobopenings.com/pro-pack/)**

= Contribute =
**You can contribute to the community by translating the plugin to your language.** Believe us, it's super-easy. Click on the link below, choose your language and start translating the strings in Development (trunk).

* **[Translate plugin to your language](https://translate.wordpress.org/projects/wp-plugins/wp-job-openings/)**

== Installation ==
1. Upload the plugin folder to the `/wp-content/plugins/` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the `Plugins` screen in WordPress

== Screenshots ==

1. Job Openings and Applications Dashboard
2. Job listing - Grid View
3. Job listing - List View
4. Job Detail View
5. Plugin Welcome Page
6. Add A Job Opening
7. Application List
8. Application Detail View
9. Email Digest
10. Job Openings List
11. General Settings
12. Job Specifications Settings
13. Notifications Template Settings

== Changelog ==

= V 3.5.2 - 2025-04-25 =
* Security fixes and code improvements.

= V 3.5.1 - 2025-04-23 =
* Fixed: Corrected load_plugin_textdomain() for compatibility with WP 6.8+.
* Minor bug fixes and code improvements.

= V 3.5.0 - 2024-11-07 =
* Added: Job listing block.
* Fixed: Translation issue in email digest.
* Minor bug fixes and code improvements.

= V 3.4.7 - 2024-09-24 =
* Fixed: Recaptcha won't reset after submitting the application form.
* Minor bug fixes and code improvements.

= V 3.4.6 - 2024-04-02 =
* Fixed: Author email template tag is not working properly in job expiry notification.
* Minor bug fixes and code improvements.

= V 3.4.5 - 2024-03-21 =
* Improved: Change notification from email to WordPress default email.
* Minor bug fixes and code improvements.

= V 3.4.4 - 2024-02-05 =
* Fixed: Structure breaks when job listing shortcode in job detail page.
* Minor bug fixes and code improvements.

= V 3.4.3 - 2023-09-15 =
* Fixed: Medium severity vulnerability (Sensitive Data Exposure via Directory Listing).
* Minor bug fixes and code improvements.

= V 3.4.2 - 2023-08-22 =
* Minor bug fixes and code improvements.

= V 3.4.1 - 2023-08-01 =
* Bug fixes

= V 3.4 - 2023-07-31 =
* Added: Job expiry notification.
* Fixed: Responsive toggle issue.
* Fixed: Unable to delete expired jobs on the job edit page.
* Improved: Expired post states added in admin listings.
* Minor bug fixes and code improvements.

= V 3.3.3 - 2022-11-02 =
* Fixed: Failed to open directory issue in Add-ons screen.
* Improved: Notifications template tags.
* Dev: Hooks for dashboard and overview data customization.
* Minor bug fixes and code improvements.

= V 3.3.2 - 2022-10-19 =
* Fixed: Job specifications settings issue when options with similar words are entered.
* Fixed: HTML content issue in notification mails for some installations.
* Improved: Mail notification template. Logo in mail notification with the link to the site homepage.

= V 3.3.1 - 2022-07-06 =
* Fixed: Uploading issue with documents exported with Google.
* Fixed: Accessibility issue in job listing filters.
* Fixed: Deprecation notice with function wp_no_robots.
* Improved: Settings error handling.
* Dev: Added functions for better debugging.
* Minor bug fixes and code improvements.

= V 3.3.0 - 2022-04-25 =
* Added: HTML editor support for notifications.
* Added: Author info in the admin job listing table.
* Fixed: HTML structure issue in the notification mail.
* Improved: Notifications mail handling.
* Improved: Multilingual support for job specifications.
* Dev: Hooks for specifications customization.
* Code improvements.

= V 3.2.1 - 2022-02-02 =
* Fixed: Search field style issues in job listing.
* Fixed: Responsive style issues with job filters.

[See changelog of previous versions](https://raw.githubusercontent.com/awsmin/wp-job-openings/master/CHANGELOG.md)

== Upgrade Notice ==

= 3.3.3 =
Bug fixes and improvements.
