.awsm-clearfix::before,
.awsm-clearfix::after {
	content: "\0020";
	display: block;
	height: 0;
	visibility: hidden;
}

.awsm-clearfix::after {
	clear: both;
}

.awsm-clearfix {
	zoom: 1;
}

.awsm-job-settings-wrap a:focus {
	box-shadow: none;
}

.awsm-admin-settings,
.awsm-admin-settings *,
.awsm-admin-settings *::before,
.awsm-admin-settings *::after {
	box-sizing: border-box;
}

.awsm-job-settings-wrap {
	padding-right: 20px;
}

.awsm-job-settings-wrap h2.nav-tab-wrapper .nav-tab {
	margin-left: 0;
	margin-right: 0.5em;
	outline: none;
	box-shadow: none;
}

.awsm-jobs-settings-section-wrapper {
	position: relative;
}

.awsm-jobs-settings-loader-container {
	margin-top: 25px;
	padding: 100px 0;
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 100%;
}

.awsm-jobs-settings-section {
	opacity: 0;
	transition: opacity .3s ease;
	visibility: hidden;
}

.awsm-jobs-settings-section.awsm-visible {
	opacity: 1;
	visibility: visible;
}

.awsm-jobs-settings-error {
	margin-top: 10px;
}

.settings-error.awsm-jobs-warning {
    border-left-color: #ffb900;
}

.awsm-nav-subtab-container ul.subsubsub {
	margin: 18px 0 10px;
}

.awsm-nav-subtab-container .subsubsub li::after {
	content: "|";
}

.awsm-nav-subtab-container .subsubsub li:last-child::after {
	content: "";
}

/* Hide WPML subtab in Settings page */
.awsm-job-settings-wrap .awsm-nav-subtab-container .icl_subsubsub {
	display: none !important;
}

.form-table th.awsm-form-head-title {
	padding: 10px 10px 10px 0;
}

.awsm-job-settings-wrap form h2 {
	font-size: 16px;
	margin: 0;
}

.awsm-job-settings-wrap form h2.awsm-section-title {
	margin: 20px 0;
}

.awsm-form-section {
	margin-bottom: 20px;
}

.awsm-form-section:last-child {
	margin-bottom: 0;
}

.awsm-form-section h3 {
	font-size: 14px;
	font-weight: 600;
	margin: 0 0 20px;
}

ul.awsm-list-inline {
	list-style: none;
	padding: 0;
	margin: 0 -5px;
}

ul.awsm-list-inline li {
	display: inline-block;
	vertical-align: middle;
	padding: 0 5px;
}

ul.awsm-list-inline label {
	display: block;
	font-size: 14px;
	font-weight: 600;
	margin: 0;
}

.awsm-check-list {
	list-style: none;
	margin: 0;
	padding: 0;
}

.awsm-check-list li {
	padding-bottom: 10px;
	margin: 0;
}

.awsm-check-list.awsm-check-list-small li {
	padding-bottom: 3px;
}

.awsm-form-bottom {
	padding: 20px 0;
}

.awsm-form-section select {
	min-width: 200px;
}

.wp-core-ui .awsm-view-captcha-btn {
	margin-left: 7px;
    vertical-align: middle;
}

.awsm-col-max-800 {
	max-width: 800px;
}

.awsm-row {
	margin: 0 -15px;
}

.awsm-row::after {
	content: "";
	display: table;
	clear: both;
}

.awsm-col {
	padding: 0 15px;
	float: left;
}

.awsm-col-full {
	width: 100%;
}

.awsm-col-half {
	width: 50%;
}

.awsm-form-group {
	margin-bottom: 20px;
}

.awsm-form-group label {
	display: block;
	margin-bottom: 10px;
	font-weight: bold;
}

.awsm-form-control {
	display: block;
	width: 100%;
}

.awsm-hidden-control {
	display: none !important;
}

.awsm-acc-section-main {
	padding: 0;
}

.awsm-acc-secton {
	background: #fff;
}

.awsm-acc-main {
	border-bottom: 2px solid rgba(213, 213, 213, 0.5);
}

.awsm-acc-head {
	padding: 15px 20px;
	border-bottom: 1px solid #eee;
	position: relative;
	cursor: pointer;
}

.awsm-acc-head h3 {
	margin: 0;
	position: relative;
	padding-left: 15px;
}

.awsm-acc-head h3::before {
	content: "";
	border-width: 6px 5px 0;
	border-style: solid;
	border-color: #000 transparent transparent;
	position: absolute;
	left: 0;
	top: calc(50% - 3px);
}

.awsm-acc-head.on h3::before {
	border-width: 0 5px 6px;
	border-color: transparent transparent #000;
}

.awsm-acc-form-switch .awsm-acc-head {
	padding-right: 100px;
	position: relative;
}

.awsm-toggle-switch {
	position: absolute;
	right: 20px;
	top: calc(50% - 8px);
	height: 16px;
}

.awsm-toggle-switch .awsm-ts-inner {
	width: 25px;
	height: 16px;
	border-radius: 10px;
	background: #cfcfcf;
	position: relative;
}

.awsm-toggle-switch input {
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
}

.awsm-toggle-switch span {
	display: inline-block;
	vertical-align: middle;
}

.awsm-toggle-switch .awsm-ts-label::before {
	content: attr(data-off);
	font-weight: 500;
}

.awsm-toggle-switch input:checked~.awsm-ts-label::before {
	content: attr(data-on);
}

.awsm-toggle-switch .awsm-ts-inner::before {
	content: "";
	width: 12px;
	height: 12px;
	background: #fff;
	border-radius: 6px;
	position: absolute;
	left: 2px;
	top: 2px;
}

.awsm-toggle-switch input:checked~.awsm-ts-inner {
	background: #82c438;
}

.awsm-toggle-switch input:checked~.awsm-ts-inner::before {
	left: 11px;
}

.awsm-acc-content {
	display: none;
	padding: 10px 20px 30px;
}

.awsm-acc-content p.submit {
	padding: 0;
	margin: 0;
}

.awsm-acc-main:first-child .awsm-acc-content {
	display: block;
}

.awsm-settings-image-field-container .awsm-settings-image {
	margin-bottom: 1.2em;
	padding: 5px;
}

.awsm-settings-image-field-container .awsm-settings-image.awsm-settings-no-image {
	border: 1px dashed #b4b9be;
	text-align: center;
	width: 100%;
	height: 180px;
}

.awsm-settings-image-field-container .awsm-settings-no-image span {
    color: #777;
    line-height: 180px;
    vertical-align: middle;
}

.awsm-settings-image-field-container .awsm-settings-image img {
	max-width: 100%;
	max-height: 100%;
}

.awsm-settings-image-field-container .awsm-settings-image-upload-button {
	margin-right: 10px;
}

.awsm-text-red,
.awsm-text-danger {
	color: #a92222 !important;
}

.awsm-text-green {
	color: #1ea508 !important;
}

.awsm-text-yellow {
	color: #e3c600 !important;
}

a.awsm-text-red {
	text-decoration: none;
}

a.awsm-text-red:hover {
	color: #db4c4c !important;
	text-decoration: none;
}

.awsm-admin-settings::after {
	content: "";
	display: table;
	clear: both;
}

.awsm-settings-col-left {
	float: left;
	width: 70%;
	padding-right: 30px;
}

.awsm-settings-col-right {
	float: left;
	width: 30%;
}

.awsm-settings-aside {
	background: #fff;
	padding: 25px 20px;
	margin-top: 58px;
}

.awsm-settings-aside h3 {
	font-size: 14px;
	font-weight: 600;
	margin: 0 0 18px;
}

.awsm-settings-aside .awsm-job-template-tag-list {
	list-style: none;
	margin: 0;
}

.awsm-settings-aside .awsm-job-template-tag-list li {
	margin-bottom: 10px;
	overflow: hidden;
}

.awsm-settings-aside .awsm-job-template-tag-list span {
	float: left;
	width: 50%;
	padding-right: 10px;
}

.awsm-settings-aside .awsm-job-template-tag-list span:last-child {
	padding: 0 0 0 10px;
	-webkit-user-select: all;
	-moz-user-select: all;
	-ms-user-select: all;
	user-select: all;
}


/* Job Applications */
/* to fix application meta box styling issue */
.awsm-application-submission-info{
	display: block;
	color: #72777C;
	font-size: 14px;
	margin: 0;
}
#awsm-job-details-meta{
	border-color: #DADFE5;
}
#awsm-job-details-meta .inside {
	margin: 0;
	padding: 0;
}

#awsm-job-details-meta .handlediv,
#awsm-job-details-meta .hndle,
#awsm-job-details-meta .postbox-header {
    display: none;
}

.post-type-awsm_job_application #post-body-content {
	margin-bottom: 0px;
}

.awsm-applicant-image-container {
	float: left;
	max-width: 213px;
	margin: 0;
	text-align: center;
	padding: 20px 23px;
}

.awsm-applicant-image {
	padding-bottom: 20px;
	text-align: center;
}
.awsm-applicant-image img{
	border-radius: 50%;
}
.awsm-applicant-image-container .button {
	padding: 5px 7px;
	height: auto;
	line-height: 1.3;
	background-color: #D1F1FF;
	border-color: #0271A1;
	color: #0271A1;
	padding: 10px;
	width: 166px;
	font-size: 14px;
}

.awsm-applicant-image-container .button span {
	display: block;
	text-transform: uppercase;
	font-size: 12px;
}

.awsm-applicant-details {
	overflow: hidden;
    min-height: 250px;
    border-left: 1px solid #DADFE5;
}

.awsm-applicant-details-list {
	list-style: none;
	padding: 0;
	margin: 0;
}

.awsm-applicant-details-list li {
	padding: 15px 15px 15px 30px;
	border-bottom: 1px solid #DADFE5;
	font-size: 14px;
	margin: 0;
}
.awsm-applicant-details-list li:last-child{
	border-bottom: none;
}

.awsm-applicant-details-list li label {
	font-weight: 600;
}

.awsm-applicant-details-list li span {
	display: block;
}

.awsm-applicant-details-list li span p:first-child {
	margin-top: 0;
}

#awsm-application-actions-meta .inside {
    margin: 0;
    padding: 0;
}

.awsm-application-rating-pub-section-disabled {
	display: flex;
}
.awsm-application-rating-pub-section-disabled .awsm-jobs-get-pro-btn{
	pointer-events: none;
	line-height: 1.8;
	display: inline-block;
	padding: 0 8px;
	min-height: 20px;
	border-radius: 3px;
	font-size: 12px;
}

.awsm-application-rating-disabled {
	flex: 1;
}

.awsm-job-stat-table {
	width: 100%;
}
.awsm-job-stat-table a{
	text-decoration: none;
}
.awsm-job-stat-table td{
	padding: 3px 0;
}
.awsm-job-stat-table td:not(:first-child){
	font-weight: bold;
}

.awsm-job-status-btn-wrapper {
	margin: 10px -15px 0;
	padding: 10px 15px 0;
	border-top: 1px solid #DADFE5;
	text-align: center;
}
.awsm-job-status-btn-wrapper a{
	margin: 0 5px;
}
.awsm-job-prev-application-btn{
	float: left;
}
.awsm-job-next-application-btn{
	float: right;
}
.awsm-job-status-btn-wrapper a.btn-disabled{
	opacity: 0.2;
	pointer-events: none;
}
.awsm-job-status-btn-wrapper span{
	display: inline-block;
	vertical-align: middle;
	color: #72777C;
	font-size: 13px;
	margin: 0 5px;
	line-height: 1.15;
}

.awsm-job-expiry-main {
	display: none;
	margin-top: 12px;
}

.awsm-job-expiry-items {
    margin: 16px 0px 4px 0px;
}

#awsm-job-expiry:checked~.awsm-job-expiry-main {
	display: block;
}

.awsm-job-expiry-items>input,
.awsm-job-expiry-items>label {
	display: inline-block;
	vertical-align: bottom;
}

.awsm-jobs-datepicker-wrapper {
	display: flex;
	align-items: center;
}

.awsm-jobs-datepicker {
	height: 28px;
}

.awsm-jobs-datepicker-wrapper .ui-datepicker-trigger {
	height: 20px;
	cursor: pointer;
	margin-left: 4px;
}

.widefat .column-awsm-photo {
	width: 32px;
}

.awsm-hide {
	display: none !important;
}

.awsm-show {
	display: block !important;
}

.awsm-row-show {
	display: table-row !important;
}

.awsm-specs{
	border-collapse: collapse;
}

.awsm-specs thead th {
	text-align: left;
	padding: 20px 0;
}

.awsm-specs tbody{
	background: #fff;
	border: 1px solid #cfcfcf;
}

.awsm-specs tbody td {
	padding: 20px 15px;
	width: 15%;
	vertical-align: top;
	background: #fff;;
	border-bottom: 1px solid #dddbdb;
}

.awsm-specs tbody tr:first-child {
	font-weight: bold;
}
.awsm-specs tbody tr:first-child td {
	padding: 10px 15px;
}

.awsm-specs tbody td:first-child {
	width: 20px;
}

.awsm-specs tbody td:nth-child(5) {
	width: 60%;
}

.awsm-specs tbody td:last-child {
	width: 10%;
}

.awsm-specs tbody td>input,
.awsm-specs tbody td>select {
	display: block;
	width: 100%;
	height: 30px;
}


.awsm-specs-drag-control {
	cursor: grab;
}

.awsm-specs .select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.awsm-specs .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple,
.awsm-specs .select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.awsm-specs .select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
	border-radius: 4px;
}

.awsm-specs .select2-results i {
	font-size: 16px;
	display: inline-block;
	vertical-align: middle;
	margin-right: 4px;
}

.awsm-specs .awsm-filters-remove-row {
	color: #d00000;
}

.medium-text {
	width: 50em;
}

.form-table .description span {
	text-decoration: underline;
}
.awsm-wpjo-form-group{
	margin-bottom: 10px;
}
.awsm-wpjo-form-group:last-child {
	margin-bottom: 0;
}
.awsm-wpjo-form-group label{
	margin-bottom: 4px;
	display: block;
}
.awsm-wpjo-form-group input:not([type='checkbox']):not([type='radio']), .awsm-wpjo-form-group select, .awsm-wpjo-form-group textarea{
	width: 100%;
	box-sizing: border-box;
}

/*------ Setup page ---*/
.awsm_job_openings_page_awsm-jobs-setup #wpbody-content{
	padding-bottom: 0;
	padding-left: 0;
	height: 100%;
}
.awsm_job_openings_page_awsm-jobs-setup #wpfooter {
	display: none;
}
.awsm_job_openings_page_awsm-jobs-setup .notice:not(.awsm-job-setup-notice),
.awsm_job_openings_page_awsm-jobs-setup div.updated {
	display: none;
}
.awsm_job_openings_page_awsm-jobs-setup #update-nag,
.awsm_job_openings_page_awsm-jobs-setup .update-nag {
	display: none;
}
.awsm-job-setup {
	min-height: 100%;
	margin-left: -20px;
	background-color: #6CFAE7;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	box-sizing: border-box;
	width: 100%;
	width: calc(100% + 20px);
	overflow: hidden;
}
.awsm-job-setup-col{
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	align-items:  center;
	width: 100%;
}
.awsm-job-setup-col:last-child{
	background-color: #fff;
	opacity: 0;
	transition: all 0.3s ease 0.6s;
}
.loaded .awsm-job-setup-col:last-child{
	opacity: 1;
}
.awsm-job-setup-col-in{
	-ms-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;
	padding: 50px 30px;
	box-sizing: border-box;
}
.awsm-job-setup-l{
	color: #000;
}
.awsm-job-setup-l h1{
	margin: 0 0 45px;
	transition: all 0.3s ease;
}
.awsm-job-setup-l h1 a{
	background: url('../img/logo-b.svg') no-repeat;
	background-size: 253px 33px;
	width: 253px;
	height: 33px;
	text-indent: -99999px;
	display: block
}
.awsm-job-setup-l p{
	font-size: 16px;
	margin-bottom: 40px;
	transition: all 0.3s ease 0.2s;
}
.awsm-job-setup-l ul{
	list-style: none;
	margin: 0;
	padding: 0;
	counter-reset: no-counter;
	transition: all 0.3s ease 0.4s;
}
.awsm-job-setup-l ul li{
	padding-left: 44px;
	position: relative;
	padding-bottom: 13px;
	min-height: 26px;
	font-size: 16px;
	line-height: 26px;
	margin: 0;
	font-weight: bold;
}
.awsm-job-setup-l ul li:last-child{
	padding-bottom: 0;
}
.awsm-job-setup-l ul li::after{
	counter-increment: no-counter;
	content: counter(no-counter);
	width: 26px;
	height: 26px;
	font-weight: bold;
	line-height: 26px;
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
	background: #000000;
	color: #fff;
	border-radius: 50%;
}
.awsm-job-setup-l h1, .awsm-job-setup-l p, .awsm-job-setup-l ul, .awsm-job-setup-col:last-child{
	opacity: 0;
	transform: translateY(-30px);
}
.loaded .awsm-job-setup-l h1, .loaded .awsm-job-setup-l p, .loaded .awsm-job-setup-l ul, .loaded .awsm-job-setup-col:last-child{
	opacity: 1;
	transform: none;
}
.awsm-job-setup-r h2{
	margin: 0 0 30px;
	font-size: 24px;
}
.awsm-job-setup-notice.notice {
	margin: 0 0 20px;
}
.awsm-job-form-group{
	margin-bottom: 30px;
}
.awsm-job-form-group label{
	display: block;
	margin-bottom: 5px;
	font-size: 14px;
	font-weight: bold;
}
.awsm-job-form-control{
	display: block;
	width: 100%;
	height: 38px;
	padding: 5px 12px;
	border-radius: 3px;
	border: 1px solid #dddfe3;
	font-style: 20px;
}
.awsm-job-form-group p{
	font-size: 12px;
	opacity: 0.6;
	margin: 5px 0 0;
}
.wp-core-ui #awsm-jobs-setup-btn{
	padding: 3px 20px;
	min-height: 32px;
    line-height: 2.30769231;
}

/*-----*/


.awsm-job-specifications-section label {
	margin-bottom: 6px;
	display: block;
}

.awsm-jobs-error-container {
	padding: 7px 0px;
}

.awsm-jobs-error {
	background: #fff;
	border-left: 4px solid #db4c4c;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .1);
	padding: 7px 12px;
}

.awsm-jobs-error p {
	margin: .5em 0;
	padding: 2px;
}

.awsm-jobs-invalid {
    padding: 3px 0px;
}

.awsm-jobs-invalid::before {
	content: "!";
	display: inline-block;
	margin-right: 4px;
	width: 14px;
	height: 14px;
	line-height: 14px;
	text-align: center;
	color: #fff;
	background: #D36230;
	border-radius: 50%;
	position: absolute;
    left: 0;
    top: 6px;
}

p.description.awsm-jobs-invalid {
    position: relative;
    margin-bottom: 0px;
    padding-bottom: 0px;
	padding-left: 18px;
}

/*
* Empty States
*/
.awsm-jobs-empty-list-page .page-title-action,
.awsm-jobs-empty-list-page #posts-filter .wp-list-table,
.awsm-jobs-empty-list-page #posts-filter .tablenav.top,
.awsm-jobs-empty-list-page .tablenav.bottom .actions,
.awsm-jobs-empty-list-page .wrap .subsubsub {
	display: none;
}
.awsm-jobs-empty-list-page #posts-filter .tablenav.bottom {
	height: auto;
}

.awsm-jobs-empty-list {
	text-align: center;
	margin: 50px 0;
}
.awsm-jobs-empty-list img{
	margin-bottom: 24px;
}
.awsm-jobs-empty-list h2{
	font-size: 23px;
	font-weight: normal;
	margin: 0 0 12px;
}
.awsm-jobs-empty-list p{
	font-size: 16px;
	color: #72777C;
	margin: 0 0 25px;
}
.wp-core-ui .awsm-jobs-empty-list .button{
	padding: 3px 20px;
}

/*
* Admin Navigation
*/

.awsm-job-admin-nav-page #wpcontent {
	padding: 0;
}

.awsm-job-admin-nav-page #wpbody-content {
	margin-top: 51px;
}

.awsm-job-admin-nav-page .wrap {
	padding-left: 20px;
}

.awsm-job-admin-nav-page.focus-on .awsm-job-admin-nav-header {
	display: none;
}

.awsm-job-admin-nav-page.focus-on #wpbody-content {
	margin-top: 0;
}

.awsm-job-admin-nav-header {
	display: flex;
    position: fixed;
    background: #fff;
    width: 100%;
	border-bottom: 1px solid #ccd0d4;
	z-index: 9989;
	padding: 10px 20px;
	align-items: center;
	height: 30px;
}

.awsm-job-admin-nav-logo a {
	margin: 0;
	background: url('../img/logo-b.svg') no-repeat;
	width: 168px;
	height: 21px;
	background-size: 168px 21px;
	text-indent: -99999px;
	display:inline-block;
}

.awsm-job-admin-nav {
	list-style: none;
	margin: 0;
	padding-left: 40px;
}

.awsm-job-admin-nav li {
	display: inline-block;
	padding: 0 20px;
	margin: 0;
}

.awsm-job-admin-nav li a {
	text-decoration: none;
	line-height: 30px;
	display: block;
}
.awsm-job-admin-nav li a.active{
	font-weight: bold;
}
.awsm-job-admin-nav li a.button{
	border:none;
	background: #000;
	color: #00d5af;
	font-weight:bold;
	padding: 0 15px;
	display: block;
	border-radius: 20px;
}
.awsm-job-admin-nav li a.button:hover, .awsm-job-admin-nav li a.button:focus, .awsm-job-admin-nav li a.button:active{
	background: #000;
	color: #00d5af;
	box-shadow: rgb(0 0 0 / 37%) 0px 3px 5px;
}

.awsm-job-addon-item {
	background: #fff;
	padding: 30px 25px;
	border: 1px solid #ddd;
	margin-bottom: 15px;
	overflow: hidden;
}

.awsm-job-addon-item,
.awsm-job-addon-item * {
	box-sizing: border-box;
}

.awsm-job-addon-item .awsm-add-ons-name a {
	text-decoration: none;
}

.awsm-job-addon-item .awsm-add-ons-name a:focus {
    box-shadow: none;
}

.awsm-job-addon-item img {
	float: left;
	max-width: 115px;
	margin-right: 25px;
}

.awsm-job-addon-item-inner {
	overflow: hidden;
}

.awsm-job-addon-item-inner h2 {
	font-size: 19px;
	color: #0f77ad;
	margin: 0 0 10px;
}

.awsm-job-addon-item-content {
	width: 36%;
	float: left;
	padding-right: 30px;
}

.awsm-job-addon-item-content p {
	margin: 0 0 15px;
	font-size: 14px;
}

.awsm-job-addon-item-features {
	float: left;
	width: 34%;
	padding-right: 30px;
	font-size: 14px;
	padding-left: 50px;
}

.awsm-job-addon-item-features ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.awsm-job-addon-item-features ul li {
	margin-bottom: 5px;
	padding-left: 20px;
	position: relative;
}

.awsm-job-addon-item-features ul i {
	position: absolute;
	left: 0;
	top: 2px;
}

.awsm-job-addon-item-info {
	float: left;
	width: 30%;
	text-align: center;
	margin-top: -20px;
}

.awsm-job-addon-item-info li p {
	margin: 0;
}

.awsm-job-addon-item-info .awsm-job-addon-price {
	margin: 0 0 5px;
	font-size: 18px;
}

.awsm-job-addon-item-info ul {
	list-style: none;
	padding: 0;
	margin: 0;
	font-size: 14px;
}

.awsm-job-addon-item-info li {
	padding-bottom: 10px;
	margin-bottom: 0;
}

.awsm-job-addon-item-info li a {
	text-decoration: none;
}

.more-awsm-plugins {
	padding-top: 50px;
}

.more-awsm-plugins * {
	box-sizing: border-box;
}

.more-awsm-plugins .awsm-row {
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	display: flexbox;
	-webkit-flex-flow: row wrap;
	flex-flow: row wrap;
}

.more-awsm-plugins .awsm-col {
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	-webkit-flex-direction: column;
}

a.awsm-plugin-item {
	display: block;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	-webkit-flex-direction: column;
	flex-grow: 1;
	-webkit-flex-grow: 1;
	color: #808080;
	text-decoration: none;
	background: #fff;
	padding: 25px;
	border-radius: 8px;
	margin-bottom: 30px;
	overflow: hidden;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

a.awsm-plugin-item:hover {
	box-shadow: 1px 1px 16px 0 rgba(0, 0, 0, 0.1);
}

a.awsm-plugin-item h3 {
	font-size: 16px;
	color: #0f77ad;
	margin: 0 0 10px;
}

a.awsm-plugin-item p {
	font-size: 16px;
	margin: 0;
}

.awsm-plugin-item-inner>img {
	float: left;
	max-width: 86px;
	margin-right: 20px;
}

.awsm-plugin-item-info {
	overflow: hidden;
}
.awsm-job-get-pro-meta-container{
	padding: 10px 10px 20px;
}

.awsm-job-get-pro-meta-container p{
	margin: 0 0 10px;
}
.awsm-job-get-pro-features{
	padding: 0;
	margin-bottom: 30px;
	list-style: none;
}
.awsm-job-get-pro-features li{
	position: relative;
	padding-left: 25px;
	font-weight: 700;
	line-height: 1.5;
	margin-bottom: 10px;
	font-weight: bold;
}
.awsm-job-get-pro-features li::before {
    content: "";
    background: url(../img/check.svg) no-repeat;
    width: 16px;
    height: 16px;
    position: absolute;
    left: 0;
    top: 5px;
    background-size: 16px;
}
.wp-core-ui .awsm-job-get-pro-meta-container .button{
	font-size: 14px;
	line-height: 1.2778;
	font-weight: 700;
	border-radius: 32px;
	color: #6CFAE4;
	padding: 12px 34px;
	background: #000;
	width: 100%;
	text-align: center;
}
.wp-core-ui .awsm-job-get-pro-meta-container .button:hover, .wp-core-ui .awsm-job-get-pro-meta-container .button:focus, .wp-core-ui .awsm-job-get-pro-meta-container .button:active{
	background: #000;
	box-shadow: 0 3px 24px 6px rgba(0,0,0,.18);
	color: #fff;
}
.awsm-application-post-status-disabled, .awsm-application-rating-disabled{
	opacity: 0.5;
}
@media (min-width: 701px) {
	.awsm-job-setup {
		position: absolute;
		min-height: 100vh;
	}
	.awsm-job-setup-col{
		width: 50%;
		min-height: 100%;
	}
	.awsm-job-setup-col:last-child{
		transform: translateX(100%);
		opacity: 1;
		transition: all 0.6s cubic-bezier(0.33, 1, 0.68, 1) 0.8s;
	}
	.awsm-job-setup-col-in{
		max-width: 460px;
		margin: 0 auto;
	}
	.awsm-job-setup-col:first-child .awsm-job-setup-col-in{
		margin-top: -135px;
	}
	.awsm-jobs-expired-post-state span {
		margin-right: 3px;
	}
}

@media (min-width: 1400px) {
	.awsm-job-setup{
		position: fixed;
		 width: calc(100% - 160px);
	}

}


@media (max-width: 1250px) {
	.awsm-settings-col-left {
		width: 100%;
		padding-right: 0;
	}

	.awsm-settings-col-right {
		width: 100%;
		max-width: 400px;
	}
}

@media (max-width: 1124px) {
	.awsm-job-addon-item-content {
		width: 100%;
		padding-right: 0;
		margin-bottom: 20px;
	}

	.awsm-job-addon-item-features {
		width: 70%;
		padding: 0 30px 0 0;
	}

	.awsm-job-addon-item-info {
		margin-top: 0;
	}
}
@media (max-width: 782px) {
	.awsm-job-admin-nav-header{
		top: 46px;
	}
	.awsm-job-admin-nav{
		padding-left: 25px;
	}
	.awsm-job-admin-nav li{
		padding: 0 14px;
	}
}
@media (max-width: 600px) {
	.awsm-job-admin-nav-header{
		position: absolute;
	}
}

@media (max-width: 700px) {
	.awsm-job-setup-main img {
		float: none;
		margin: 0 0 30px;
	}

	.awsm-welcome-points .awsm-col,
	.more-awsm-plugins .awsm-col {
		width: 100%;
	}

	.awsm-welcome-points .awsm-col {
		text-align: center;
	}

	.awsm-welcome-point-image {
		margin: 0 auto !important;
	}

	.awsm-welcome-point-content {
		margin: 0 auto;
	}

	.awsm-welcome-point .awsm-col {
		margin-bottom: 50px;
	}

	.awsm-job-addon-item-features {
		width: 100%;
		padding: 0;
	}

	.awsm-job-addon-item-info {
		margin-top: 30px;
		width: 100%;
		text-align: left;
	}
	.awsm-job-admin-nav-logo a{
		width: 24px;
	}
	.awsm-job-admin-nav{
		padding-left: 15px;
	}
	.awsm-job-admin-nav li{
		padding: 0 10px;
	}
}
@media (max-width: 510px) {
	.awsm-job-admin-nav-logo a{
		display: none;
	}
	.awsm-job-admin-nav{
		padding-left: 0;
	}
}

@media (max-width: 500px) {

	.awsm-plugin-item-inner>img,
	.awsm-job-addon-item img {
		float: none;
		margin: 0 0 20px;
	}
}
