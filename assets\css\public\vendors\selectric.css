/*======================================
  Selectric v1.13.0
======================================*/

.awsm-selectric-wrapper {
  position: relative;
  cursor: pointer;
  min-width: 160px;
}

.awsm-selectric-responsive {
  width: 100%;
}

.awsm-selectric {
  border: 1px solid #dddfe3;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 5%);
  background: #fff;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.awsm-selectric .label {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 48px 0 10px;
  padding: 0;
  font-size: 12px;
  line-height: 46px;
  color: #444;
  height: 46px;
  text-align: left;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.awsm-selectric .awsm-selectric-arrow-drop {
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 38px;
  height: 100%;
  text-indent: -99999px;
  text-align: center;
}

.awsm-selectric .awsm-selectric-arrow-drop:after {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-top-color: #444;
  border-bottom: none;
}

.awsm-selectric-focus .awsm-selectric {
  border-color: #aaaaaa;
}

.awsm-selectric-hover .awsm-selectric {
  border-color: #dbdbdb;
}


.awsm-selectric-hover .awsm-selectric .awsm-selectric-arrow-drop:after {
  border-top-color: #444;
}

.awsm-selectric-open {
  z-index: 9999;
}

.awsm-selectric-open .awsm-selectric {
  border-color: #dbdbdb;
}

.awsm-selectric-open .awsm-selectric-items {
  display: block;
}

.awsm-selectric-disabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.awsm-selectric-hide-select {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

.awsm-selectric-hide-select select {
  position: absolute;
  left: -100%;
}

.awsm-selectric-hide-select.awsm-selectric-is-native {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.awsm-selectric-hide-select.awsm-selectric-is-native select {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  border: none;
  z-index: 1;
  box-sizing: border-box;
  opacity: 0;
}

.awsm-selectric-input {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  *font: 0/0 a !important;
  background: none !important;
}

.awsm-selectric-temp-show {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* Items box */
.awsm-selectric-items {
  display: none;
  position: absolute;
  top: calc(100% - 1px);
  left: 0;
  background: #fff;
  border: 1px solid #dbdbdb;
  z-index: -1;
  box-shadow: 0 0 5px -6px rgba(0,0,0,0.4);
  border-top: none;;
}

.awsm-selectric-items .awsm-selectric-scroll {
  height: 100%;
  overflow: auto;
}

.awsm-selectric-above .awsm-selectric-items {
  top: auto;
  bottom: calc(100% - 1px);
  border-radius: 4px 4px 0 0;
  border-bottom: none;
}

.awsm-selectric-items ul, .awsm-selectric-items li {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 12px;
  line-height: 20px;
  min-height: 20px;
}

.awsm-selectric-items li {
  display: block;
  padding: 10px !important;
  color: #666;
  cursor: pointer;
}

.awsm-selectric-items li.selected {
  background: #efefef;
  color: #444;
}

.awsm-selectric-items li.highlighted {
  background: #efefef;
  color: #444;
}

.awsm-selectric-items li:hover {
  background: #d7d7d7;
  color: #444;
}

.awsm-selectric-items .disabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default !important;
  background: none !important;
  color: #666 !important;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.awsm-selectric-items .awsm-selectric-group .awsm-selectric-group-label {
  font-weight: bold;
  padding-left: 10px;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background: none;
  color: #444;
}

.awsm-selectric-items .awsm-selectric-group.disabled li {
  filter: alpha(opacity=100);
  opacity: 1;
}

.awsm-selectric-items .awsm-selectric-group li {
  padding-left: 25px;
}
.awsm-selectric-awsm-job-select-control-multiple .awsm-selectric-items li{
  padding-left: 34px !important;
  position: relative;
}
.awsm-selectric-awsm-job-select-control-multiple .awsm-selectric-items li::before{
  content: "";
  width: 14px;
  height: 14px;
  border: 1px solid #dddfe3;
  position: absolute;
  left: 10px;
  top: calc(50% - 7px);
  border-radius: 2px;
}
.awsm-selectric-awsm-job-select-control-multiple .awsm-selectric-items li::after{
  content: "";
  width: 14px;
  height: 14px;
  position: absolute;
  left: 10px;
  top: calc(50% - 7px);
  border-radius: 2px;
  background: #3e8ed0 url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMXB4IiB2aWV3Qm94PSIwIDAgMTIgMTEiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dGljay1zdmdyZXBvLWNvbTwvdGl0bGU+CiAgICA8ZyBpZD0iUGFnZS0xIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0idGljay1zdmdyZXBvLWNvbSIgZmlsbD0iI0ZGRkZGRiIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlBhdGgiIHBvaW50cz0iMTEuMDc1NTgzNyAwIDQuODQ0ODQwODIgOC45NzE3ODc3NiAwLjcxMTI4MTYzMyA1LjU5NzExODM3IDAgNi40Njg4MDgxNiA1LjA3NTU4MzY3IDEwLjYxMjYwNDEgMTIgMC42NDE2ODE2MzMiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==') no-repeat center;
  background-size: 8px;
  opacity: 0;
  transition: all 0.3s ease;
}
.awsm-selectric-awsm-job-select-control-multiple .awsm-selectric-items li.selected{
  color: #666;
  background: #fff;
}
.awsm-selectric-awsm-job-select-control-multiple .awsm-selectric-items li.selected::after{
  opacity: 1;
}