!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Clipboard=e()}(function(){return function i(o,s,r){function a(n,e){if(!s[n]){if(!o[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(l)return l(n,!0);e=new Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e}t=s[n]={exports:{}};o[n][0].call(t.exports,function(e){var t=o[n][1][e];return a(t||e)},t,t.exports,i,o,s,r)}return s[n].exports}for(var l="function"==typeof require&&require,e=0;e<r.length;e++)a(r[e]);return a}({1:[function(e,t,n){var o=e("matches-selector");t.exports=function(e,t,n){for(var i=n?e:e.parentNode;i&&i!==document;){if(o(i,t))return i;i=i.parentNode}}},{"matches-selector":5}],2:[function(e,t,n){var r=e("closest");t.exports=function(e,t,n,i,o){var s=function(t,n,e,i){return function(e){e.delegateTarget=r(e.target,n,!0),e.delegateTarget&&i.call(t,e)}}.apply(this,arguments);return e.addEventListener(n,s,o),{destroy:function(){e.removeEventListener(n,s,o)}}}},{closest:1}],3:[function(e,t,n){n.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},n.nodeList=function(e){var t=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===t||"[object HTMLCollection]"===t)&&"length"in e&&(0===e.length||n.node(e[0]))},n.string=function(e){return"string"==typeof e||e instanceof String},n.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},{}],4:[function(e,t,n){var c=e("./is"),u=e("delegate");t.exports=function(e,t,n){if(!e&&!t&&!n)throw new Error("Missing required arguments");if(!c.string(t))throw new TypeError("Second argument must be a String");if(!c.fn(n))throw new TypeError("Third argument must be a Function");if(c.node(e))return o=t,s=n,(i=e).addEventListener(o,s),{destroy:function(){i.removeEventListener(o,s)}};var i,o,s,r,a,l;if(c.nodeList(e))return r=e,a=t,l=n,Array.prototype.forEach.call(r,function(e){e.addEventListener(a,l)}),{destroy:function(){Array.prototype.forEach.call(r,function(e){e.removeEventListener(a,l)})}};if(c.string(e))return u(document.body,e,t,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},{"./is":3,delegate:2}],5:[function(e,t,n){var i=Element.prototype,o=i.matchesSelector||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||i.oMatchesSelector;t.exports=function(e,t){if(o)return o.call(e,t);for(var n=e.parentNode.querySelectorAll(t),i=0;i<n.length;++i)if(n[i]==e)return!0;return!1}},{}],6:[function(e,t,n){t.exports=function(e){var t,n;return e="INPUT"===e.nodeName||"TEXTAREA"===e.nodeName?(e.focus(),e.setSelectionRange(0,e.value.length),e.value):(e.hasAttribute("contenteditable")&&e.focus(),t=window.getSelection(),(n=document.createRange()).selectNodeContents(e),t.removeAllRanges(),t.addRange(n),t.toString())}},{}],7:[function(e,t,n){function i(){}i.prototype={on:function(e,t,n){var i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){function i(){o.off(e,i),t.apply(n,arguments)}var o=this;return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),i=0,o=n.length;i<o;i++)n[i].fn.apply(n[i].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),i=n[e],o=[];if(i&&t)for(var s=0,r=i.length;s<r;s++)i[s].fn!==t&&i[s].fn._!==t&&o.push(i[s]);return o.length?n[e]=o:delete n[e],this}},t.exports=i},{}],8:[function(e,t,n){var i,o;i=this,o=function(e,t){var n=(t=t)&&t.__esModule?t:{default:t},i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},t=function(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e};function o(e){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.resolveOptions(e),this.initSelection()}function s(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}o.prototype.resolveOptions=function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];this.action=e.action,this.emitter=e.emitter,this.target=e.target,this.text=e.text,this.trigger=e.trigger,this.selectedText=""},o.prototype.initSelection=function(){this.text?this.selectFake():this.target&&this.selectTarget()},o.prototype.selectFake=function(){var e=this,t="rtl"==document.documentElement.getAttribute("dir");this.removeFake(),this.fakeHandlerCallback=function(){return e.removeFake()},this.fakeHandler=document.body.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[t?"right":"left"]="-9999px",this.fakeElem.style.top=(window.pageYOffset||document.documentElement.scrollTop)+"px",this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,document.body.appendChild(this.fakeElem),this.selectedText=(0,n.default)(this.fakeElem),this.copyText()},o.prototype.removeFake=function(){this.fakeHandler&&(document.body.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(document.body.removeChild(this.fakeElem),this.fakeElem=null)},o.prototype.selectTarget=function(){this.selectedText=(0,n.default)(this.target),this.copyText()},o.prototype.copyText=function(){var t=void 0;try{t=document.execCommand(this.action)}catch(e){t=!1}this.handleResult(t)},o.prototype.handleResult=function(e){e?this.emitter.emit("success",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)}):this.emitter.emit("error",{action:this.action,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})},o.prototype.clearSelection=function(){this.target&&this.target.blur(),window.getSelection().removeAllRanges()},o.prototype.destroy=function(){this.removeFake()},t(o,[{key:"action",set:function(){if(this._action=arguments.length<=0||void 0===arguments[0]?"copy":arguments[0],"copy"!==this._action&&"cut"!==this._action)throw new Error('Invalid "action" value, use either "copy" or "cut"')},get:function(){return this._action}},{key:"target",set:function(e){if(void 0!==e){if(!e||"object"!==(void 0===e?"undefined":i(e))||1!==e.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===this.action&&e.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===this.action&&(e.hasAttribute("readonly")||e.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes');this._target=e}},get:function(){return this._target}}]),e.exports=o},void 0!==n?o(t,e("select")):(o(o={exports:{}},i.select),i.clipboardAction=o.exports)},{select:6}],9:[function(e,t,n){var i,o;i=this,o=function(e,t,n,i){function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t){e="data-clipboard-"+e;if(t.hasAttribute(e))return t.getAttribute(e)}var r=o(t),t=o(n),a=o(i),n=(i=>{function o(e,t){var n;if(this instanceof o)return(n=((e,t)=>{if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")})(this,i.call(this))).resolveOptions(t),n.listenClick(e),n;throw new TypeError("Cannot call a class as a function")}var e=o,t=i;if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t),o.prototype.resolveOptions=function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText},o.prototype.listenClick=function(e){var t=this;this.listener=(0,a.default)(e,"click",function(e){return t.onClick(e)})},o.prototype.onClick=function(e){e=e.delegateTarget||e.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new r.default({action:this.action(e),target:this.target(e),text:this.text(e),trigger:e,emitter:this})},o.prototype.defaultAction=function(e){return s("action",e)},o.prototype.defaultTarget=function(e){e=s("target",e);return e?document.querySelector(e):void 0},o.prototype.defaultText=function(e){return s("text",e)},o.prototype.destroy=function(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)},o})(t.default);e.exports=n},void 0!==n?o(t,e("./clipboard-action"),e("tiny-emitter"),e("good-listener")):(o(o={exports:{}},i.clipboardAction,i.tinyEmitter,i.goodListener),i.clipboard=o.exports)},{"./clipboard-action":8,"good-listener":4,"tiny-emitter":7}]},{},[9])(9)}),(n=>{"function"==typeof define&&define.amd?define(["jquery"],n):"object"==typeof module&&module.exports?module.exports=function(e,t){return void 0===t&&(t="undefined"!=typeof window?require("jquery"):require("jquery")(e)),n(t),t}:n(jQuery)})(function(t){(u=t&&t.fn&&t.fn.select2&&t.fn.select2.amd?t.fn.select2.amd:u)&&u.requirejs||(u?e=u:u={},g={},m={},v={},y={},n=Object.prototype.hasOwnProperty,i=[].slice,w=/\.js$/,h=function(e,t){var n,i,o=c(e),s=o[0],t=t[1];return e=o[1],s&&(n=x(s=l(s,t))),s?e=n&&n.normalize?n.normalize(e,(i=t,function(e){return l(e,i)})):l(e,t):(s=(o=c(e=l(e,t)))[0],e=o[1],s&&(n=x(s))),{f:s?s+"!"+e:e,n:e,pr:s,p:n}},f={require:function(e){return _(e)},exports:function(e){var t=g[e];return void 0!==t?t:g[e]={}},module:function(e){return{id:e,uri:"",exports:g[e],config:(t=e,function(){return v&&v.config&&v.config[t]||{}})};var t}},s=function(e,t,n,i){var o,s,r,a,l,c=[],u=typeof n,d=A(i=i||e);if("undefined"==u||"function"==u){for(t=!t.length&&n.length?["require","exports","module"]:t,a=0;a<t.length;a+=1)if("require"===(s=(r=h(t[a],d)).f))c[a]=f.require(e);else if("exports"===s)c[a]=f.exports(e),l=!0;else if("module"===s)o=c[a]=f.module(e);else if(b(g,s)||b(m,s)||b(y,s))c[a]=x(s);else{if(!r.p)throw new Error(e+" missing "+s);r.p.load(r.n,_(i,!0),(t=>function(e){g[t]=e})(s),{}),c[a]=g[s]}u=n?n.apply(g[e],c):void 0,e&&(o&&o.exports!==p&&o.exports!==g[e]?g[e]=o.exports:u===p&&l||(g[e]=u))}else e&&(g[e]=n)},o=e=r=function(e,t,n,i,o){if("string"==typeof e)return f[e]?f[e](t):x(h(e,A(t)).f);if(!e.splice){if((v=e).deps&&r(v.deps,v.callback),!t)return;t.splice?(e=t,t=n,n=null):e=p}return t=t||function(){},"function"==typeof n&&(n=i,i=o),i?s(p,e,t,n):setTimeout(function(){s(p,e,t,n)},4),r},r.config=function(e){return r(e)},o._defined=g,(a=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),b(g,e)||b(m,e)||(m[e]=[e,t,n])}).amd={jQuery:!0},u.requirejs=o,u.require=e,u.define=a),u.define("almond",function(){}),u.define("jquery",[],function(){var e=t||$;return null==e&&console&&console.error,e}),u.define("select2/utils",["jquery"],function(s){var i={};function c(e){var t,n=e.prototype,i=[];for(t in n)"function"==typeof n[t]&&"constructor"!==t&&i.push(t);return i}i.Extend=function(e,t){var n,i={}.hasOwnProperty;function o(){this.constructor=e}for(n in t)i.call(t,n)&&(e[n]=t[n]);return o.prototype=t.prototype,e.prototype=new o,e.__super__=t.prototype,e},i.Decorate=function(i,o){var e=c(o),t=c(i);function s(){var e=Array.prototype.unshift,t=o.prototype.constructor.length,n=i.prototype.constructor;0<t&&(e.call(arguments,i.prototype.constructor),n=o.prototype.constructor),n.apply(this,arguments)}o.displayName=i.displayName,s.prototype=new function(){this.constructor=s};for(var n=0;n<t.length;n++){var r=t[n];s.prototype[r]=i.prototype[r]}for(var a=0;a<e.length;a++){var l=e[a];s.prototype[l]=(e=>{var t=function(){},n=(e in s.prototype&&(t=s.prototype[e]),o.prototype[e]);return function(){return Array.prototype.unshift.call(arguments,t),n.apply(this,arguments)}})(l)}return s};function e(){this.listeners={}}e.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},e.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},0===(n=null==n?[]:n).length&&n.push({}),(n[0]._type=e)in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},e.prototype.invoke=function(e,t){for(var n=0,i=e.length;n<i;n++)e[n].apply(this,t)},i.Observable=e,i.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},i.bind=function(e,t){return function(){e.apply(t,arguments)}},i._convertData=function(e){for(var t in e){var n=t.split("-"),i=e;if(1!==n.length){for(var o=0;o<n.length;o++){var s=n[o];(s=s.substring(0,1).toLowerCase()+s.substring(1))in i||(i[s]={}),o==n.length-1&&(i[s]=e[t]),i=i[s]}delete e[t]}}return e},i.hasScroll=function(e,t){var n=s(t),i=t.style.overflowX,o=t.style.overflowY;return(i!==o||"hidden"!==o&&"visible"!==o)&&("scroll"===i||"scroll"===o||n.innerHeight()<t.scrollHeight||n.innerWidth()<t.scrollWidth)},i.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,function(e){return t[e]})},i.__cache={};var n=0;return i.GetUniqueElementId=function(e){var t=e.getAttribute("data-select2-id");return null==t&&(t=e.id?"select2-data-"+e.id:"select2-data-"+(++n).toString()+"-"+i.generateChars(4),e.setAttribute("data-select2-id",t)),t},i.StoreData=function(e,t,n){e=i.GetUniqueElementId(e);i.__cache[e]||(i.__cache[e]={}),i.__cache[e][t]=n},i.GetData=function(e,t){var n=i.GetUniqueElementId(e);return t?i.__cache[n]&&null!=i.__cache[n][t]?i.__cache[n][t]:s(e).data(t):i.__cache[n]},i.RemoveData=function(e){var t=i.GetUniqueElementId(e);null!=i.__cache[t]&&delete i.__cache[t],e.removeAttribute("data-select2-id")},i.copyNonInternalCssClasses=function(e,t){var n=(n=e.getAttribute("class").trim().split(/\s+/)).filter(function(e){return 0===e.indexOf("select2-")}),t=(t=t.getAttribute("class").trim().split(/\s+/)).filter(function(e){return 0!==e.indexOf("select2-")}),n=n.concat(t);e.setAttribute("class",n.join(" "))},i}),u.define("select2/results",["jquery","./utils"],function(u,d){function i(e,t,n){this.$element=e,this.data=n,this.options=t,i.__super__.constructor.call(this)}return d.Extend(i,d.Observable),i.prototype.render=function(){var e=u('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&e.attr("aria-multiselectable","true"),this.$results=e},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(e){var t=this.options.get("escapeMarkup"),n=(this.clear(),this.hideLoading(),u('<li role="alert" aria-live="assertive" class="select2-results__option"></li>')),i=this.options.get("translations").get(e.message);n.append(t(i(e.args))),n[0].className+=" select2-results__message",this.$results.append(n)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(e){this.hideLoading();var t=[];if(null==e.results||0===e.results.length)0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"});else{e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var i=e.results[n],i=this.option(i);t.push(i)}this.$results.append(t)}},i.prototype.position=function(e,t){t.find(".select2-results").append(e)},i.prototype.sort=function(e){return this.options.get("sorter")(e)},i.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option--selectable"),t=e.filter(".select2-results__option--selected");(0<t.length?t:e).first().trigger("mouseenter"),this.ensureHighlightVisible()},i.prototype.setClasses=function(){var t=this;this.data.current(function(e){var i=e.map(function(e){return e.id.toString()});t.$results.find(".select2-results__option--selectable").each(function(){var e=u(this),t=d.GetData(this,"data"),n=""+t.id;null!=t.element&&t.element.selected||null==t.element&&-1<i.indexOf(n)?(this.classList.add("select2-results__option--selected"),e.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),e.attr("aria-selected","false"))})})},i.prototype.showLoading=function(e){this.hideLoading();e={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},e=this.option(e);e.className+=" loading-results",this.$results.prepend(e)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(e){var t,n=document.createElement("li"),i=(n.classList.add("select2-results__option"),n.classList.add("select2-results__option--selectable"),{role:"option"}),o=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(t in(null!=e.element&&o.call(e.element,":disabled")||null==e.element&&e.disabled)&&(i["aria-disabled"]="true",n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--disabled")),null==e.id&&n.classList.remove("select2-results__option--selectable"),null!=e._resultId&&(n.id=e._resultId),e.title&&(n.title=e.title),e.children&&(i.role="group",i["aria-label"]=e.text,n.classList.remove("select2-results__option--selectable"),n.classList.add("select2-results__option--group")),i)n.setAttribute(t,i[t]);if(e.children){for(var o=u(n),s=document.createElement("strong"),r=(s.className="select2-results__group",this.template(e,s),[]),a=0;a<e.children.length;a++){var l=e.children[a],l=this.option(l);r.push(l)}var c=u("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"none"});c.append(r),o.append(s),o.append(c)}else this.template(e,n);return d.StoreData(n,"data",e),n},i.prototype.bind=function(t,e){var o=this,n=t.id+"-results";this.$results.attr("id",n),t.on("results:all",function(e){o.clear(),o.append(e.data),t.isOpen()&&(o.setClasses(),o.highlightFirstItem())}),t.on("results:append",function(e){o.append(e.data),t.isOpen()&&o.setClasses()}),t.on("query",function(e){o.hideMessages(),o.showLoading(e)}),t.on("select",function(){t.isOpen()&&(o.setClasses(),o.options.get("scrollAfterSelect"))&&o.highlightFirstItem()}),t.on("unselect",function(){t.isOpen()&&(o.setClasses(),o.options.get("scrollAfterSelect"))&&o.highlightFirstItem()}),t.on("open",function(){o.$results.attr("aria-expanded","true"),o.$results.attr("aria-hidden","false"),o.setClasses(),o.ensureHighlightVisible()}),t.on("close",function(){o.$results.attr("aria-expanded","false"),o.$results.attr("aria-hidden","true"),o.$results.removeAttr("aria-activedescendant")}),t.on("results:toggle",function(){var e=o.getHighlightedResults();0!==e.length&&e.trigger("mouseup")}),t.on("results:select",function(){var e,t=o.getHighlightedResults();0!==t.length&&(e=d.GetData(t[0],"data"),t.hasClass("select2-results__option--selected")?o.trigger("close",{}):o.trigger("select",{data:e}))}),t.on("results:previous",function(){var e,t=o.getHighlightedResults(),n=o.$results.find(".select2-results__option--selectable"),i=n.index(t);i<=0||(i=i-1,0===t.length&&(i=0),(t=n.eq(i)).trigger("mouseenter"),n=o.$results.offset().top,t=t.offset().top,e=o.$results.scrollTop()+(t-n),0===i?o.$results.scrollTop(0):t-n<0&&o.$results.scrollTop(e))}),t.on("results:next",function(){var e,t,n=o.getHighlightedResults(),i=o.$results.find(".select2-results__option--selectable"),n=i.index(n)+1;n>=i.length||((i=i.eq(n)).trigger("mouseenter"),e=o.$results.offset().top+o.$results.outerHeight(!1),i=i.offset().top+i.outerHeight(!1),t=o.$results.scrollTop()+i-e,0===n?o.$results.scrollTop(0):e<i&&o.$results.scrollTop(t))}),t.on("results:focus",function(e){e.element[0].classList.add("select2-results__option--highlighted"),e.element[0].setAttribute("aria-selected","true")}),t.on("results:message",function(e){o.displayMessage(e)}),u.fn.mousewheel&&this.$results.on("mousewheel",function(e){var t=o.$results.scrollTop(),n=o.$results.get(0).scrollHeight-t+e.deltaY,t=0<e.deltaY&&t-e.deltaY<=0,n=e.deltaY<0&&n<=o.$results.height();t?(o.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):n&&(o.$results.scrollTop(o.$results.get(0).scrollHeight-o.$results.height()),e.preventDefault(),e.stopPropagation())}),this.$results.on("mouseup",".select2-results__option--selectable",function(e){var t=u(this),n=d.GetData(this,"data");t.hasClass("select2-results__option--selected")?o.options.get("multiple")?o.trigger("unselect",{originalEvent:e,data:n}):o.trigger("close",{}):o.trigger("select",{originalEvent:e,data:n})}),this.$results.on("mouseenter",".select2-results__option--selectable",function(e){var t=d.GetData(this,"data");o.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),o.trigger("results:focus",{data:t,element:u(this)})})},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var e,t,n,i,o=this.getHighlightedResults();0!==o.length&&(e=this.$results.find(".select2-results__option--selectable").index(o),t=this.$results.offset().top,i=o.offset().top,n=this.$results.scrollTop()+(i-t),i=i-t,n-=2*o.outerHeight(!1),e<=2?this.$results.scrollTop(0):(i>this.$results.outerHeight()||i<0)&&this.$results.scrollTop(n))},i.prototype.template=function(e,t){var n=this.options.get("templateResult"),i=this.options.get("escapeMarkup"),n=n(e,t);null==n?t.style.display="none":"string"==typeof n?t.innerHTML=i(n):u(t).append(n)},i}),u.define("select2/keys",[],function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}}),u.define("select2/selection/base",["jquery","../utils","../keys"],function(n,i,o){function s(e,t){this.$element=e,this.options=t,s.__super__.constructor.call(this)}return i.Extend(s,i.Observable),s.prototype.render=function(){var e=n('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=i.GetData(this.$element[0],"old-tabindex")?this._tabindex=i.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),e.attr("title",this.$element.attr("title")),e.attr("tabindex",this._tabindex),e.attr("aria-disabled","false"),this.$selection=e},s.prototype.bind=function(e,t){var n=this,i=e.id+"-results";this.container=e,this.$selection.on("focus",function(e){n.trigger("focus",e)}),this.$selection.on("blur",function(e){n._handleBlur(e)}),this.$selection.on("keydown",function(e){n.trigger("keypress",e),e.which===o.SPACE&&e.preventDefault()}),e.on("results:focus",function(e){n.$selection.attr("aria-activedescendant",e.data._resultId)}),e.on("selection:update",function(e){n.update(e.data)}),e.on("open",function(){n.$selection.attr("aria-expanded","true"),n.$selection.attr("aria-owns",i),n._attachCloseHandler(e)}),e.on("close",function(){n.$selection.attr("aria-expanded","false"),n.$selection.removeAttr("aria-activedescendant"),n.$selection.removeAttr("aria-owns"),n.$selection.trigger("focus"),n._detachCloseHandler(e)}),e.on("enable",function(){n.$selection.attr("tabindex",n._tabindex),n.$selection.attr("aria-disabled","false")}),e.on("disable",function(){n.$selection.attr("tabindex","-1"),n.$selection.attr("aria-disabled","true")})},s.prototype._handleBlur=function(e){var t=this;window.setTimeout(function(){document.activeElement==t.$selection[0]||n.contains(t.$selection[0],document.activeElement)||t.trigger("blur",e)},1)},s.prototype._attachCloseHandler=function(e){n(document.body).on("mousedown.select2."+e.id,function(e){var t=n(e.target).closest(".select2");n(".select2.select2-container--open").each(function(){this!=t[0]&&i.GetData(this,"element").awsmSelect2("close")})})},s.prototype._detachCloseHandler=function(e){n(document.body).off("mousedown.select2."+e.id)},s.prototype.position=function(e,t){t.find(".selection").append(e)},s.prototype.destroy=function(){this._detachCloseHandler(this.container)},s.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},s.prototype.isEnabled=function(){return!this.isDisabled()},s.prototype.isDisabled=function(){return this.options.get("disabled")},s}),u.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(e,t,n,i){function o(){o.__super__.constructor.apply(this,arguments)}return n.Extend(o,t),o.prototype.render=function(){var e=o.__super__.render.call(this);return e[0].classList.add("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},o.prototype.bind=function(t,e){var n=this,i=(o.__super__.bind.apply(this,arguments),t.id+"-container");this.$selection.find(".select2-selection__rendered").attr("id",i).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",i),this.$selection.attr("aria-controls",i),this.$selection.on("mousedown",function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})}),this.$selection.on("focus",function(e){}),this.$selection.on("blur",function(e){}),t.on("focus",function(e){t.isOpen()||n.$selection.trigger("focus")})},o.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},o.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},o.prototype.selectionContainer=function(){return e("<span></span>")},o.prototype.update=function(e){var t,n;0===e.length?this.clear():(e=e[0],t=this.$selection.find(".select2-selection__rendered"),n=this.display(e,t),t.empty().append(n),(n=e.title||e.text)?t.attr("title",n):t.removeAttr("title"))},o}),u.define("select2/selection/multiple",["jquery","./base","../utils"],function(i,e,c){function o(e,t){o.__super__.constructor.apply(this,arguments)}return c.Extend(o,e),o.prototype.render=function(){var e=o.__super__.render.call(this);return e[0].classList.add("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered"></ul>'),e},o.prototype.bind=function(e,t){var n=this,e=(o.__super__.bind.apply(this,arguments),e.id+"-container");this.$selection.find(".select2-selection__rendered").attr("id",e),this.$selection.on("click",function(e){n.trigger("toggle",{originalEvent:e})}),this.$selection.on("click",".select2-selection__choice__remove",function(e){var t;n.isDisabled()||(t=i(this).parent(),t=c.GetData(t[0],"data"),n.trigger("unselect",{originalEvent:e,data:t}))}),this.$selection.on("keydown",".select2-selection__choice__remove",function(e){n.isDisabled()||e.stopPropagation()})},o.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},o.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},o.prototype.selectionContainer=function(){return i('<li class="select2-selection__choice"><button type="button" class="select2-selection__choice__remove" tabindex="-1"><span aria-hidden="true">&times;</span></button><span class="select2-selection__choice__display"></span></li>')},o.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],n=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",i=0;i<e.length;i++){var o=e[i],s=this.selectionContainer(),r=this.display(o,s),a=n+c.generateChars(4)+"-",r=(o.id?a+=o.id:a+=c.generateChars(4),s.find(".select2-selection__choice__display").append(r).attr("id",a),o.title||o.text),r=(r&&s.attr("title",r),this.options.get("translations").get("removeItem")),l=s.find(".select2-selection__choice__remove");l.attr("title",r()),l.attr("aria-label",r()),l.attr("aria-describedby",a),c.StoreData(s[0],"data",o),t.push(s)}this.$selection.find(".select2-selection__rendered").append(t)}},o}),u.define("select2/selection/placeholder",[],function(){function e(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return e.prototype.normalizePlaceholder=function(e,t){return t="string"==typeof t?{id:"",text:t}:t},e.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer(),t=(n.html(this.display(t)),n[0].classList.add("select2-selection__placeholder"),n[0].classList.remove("select2-selection__choice"),t.title||t.text||n.text());return this.$selection.find(".select2-selection__rendered").attr("title",t),n},e.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(1<t.length||n)return e.call(this,t);this.clear();n=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(n)},e}),u.define("select2/selection/allowClear",["jquery","../keys","../utils"],function(o,i,a){function e(){}return e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error,this.$selection.on("mousedown",".select2-selection__clear",function(e){i._handleClear(e)}),t.on("keypress",function(e){i._handleKeyboardClear(e,t)})},e.prototype._handleClear=function(e,t){if(!this.isDisabled()){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){t.stopPropagation();var i=a.GetData(n[0],"data"),o=this.$element.val(),s=(this.$element.val(this.placeholder.id),{data:i});if(this.trigger("clear",s),s.prevented)this.$element.val(o);else{for(var r=0;r<i.length;r++)if(s={data:i[r]},this.trigger("unselect",s),s.prevented)return void this.$element.val(o);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},e.prototype._handleKeyboardClear=function(e,t,n){n.isOpen()||t.which!=i.DELETE&&t.which!=i.BACKSPACE||this._handleClear(t)},e.prototype.update=function(e,t){var n,i;e.call(this,t),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),0<this.$selection.find(".select2-selection__placeholder").length||0===t.length||(e=this.$selection.find(".select2-selection__rendered").attr("id"),n=this.options.get("translations").get("removeAllItems"),(i=o('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;</span></button>')).attr("title",n()),i.attr("aria-label",n()),i.attr("aria-describedby",e),a.StoreData(i[0],"data",t),this.$selection.prepend(i),this.$selection[0].classList.add("select2-selection--clearable"))},e}),u.define("select2/selection/search",["jquery","../utils","../keys"],function(i,a,l){function e(e,t,n){e.call(this,t,n)}return e.prototype.render=function(e){var t=this.options.get("translations").get("search"),n=i('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ></textarea></span>'),n=(this.$searchContainer=n,this.$search=n.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",t()),e.call(this));return this._transferTabIndex(),n.append(this.$searchContainer),n},e.prototype.bind=function(e,t,n){var i=this,o=t.id+"-results",s=t.id+"-container",e=(e.call(this,t,n),i.$search.attr("aria-describedby",s),t.on("open",function(){i.$search.attr("aria-controls",o),i.$search.trigger("focus")}),t.on("close",function(){i.$search.val(""),i.resizeSearch(),i.$search.removeAttr("aria-controls"),i.$search.removeAttr("aria-activedescendant"),i.$search.trigger("focus")}),t.on("enable",function(){i.$search.prop("disabled",!1),i._transferTabIndex()}),t.on("disable",function(){i.$search.prop("disabled",!0)}),t.on("focus",function(e){i.$search.trigger("focus")}),t.on("results:focus",function(e){e.data._resultId?i.$search.attr("aria-activedescendant",e.data._resultId):i.$search.removeAttr("aria-activedescendant")}),this.$selection.on("focusin",".select2-search--inline",function(e){i.trigger("focus",e)}),this.$selection.on("focusout",".select2-search--inline",function(e){i._handleBlur(e)}),this.$selection.on("keydown",".select2-search--inline",function(e){var t;e.stopPropagation(),i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented(),e.which===l.BACKSPACE&&""===i.$search.val()&&0<(t=i.$selection.find(".select2-selection__choice").last()).length&&(t=a.GetData(t[0],"data"),i.searchRemoveChoice(t),e.preventDefault())}),this.$selection.on("click",".select2-search--inline",function(e){i.$search.val()&&e.stopPropagation()}),document.documentMode),r=e&&e<=11;this.$selection.on("input.searchcheck",".select2-search--inline",function(e){r?i.$selection.off("input.search input.searchcheck"):i.$selection.off("keyup.search")}),this.$selection.on("keyup.search input.search",".select2-search--inline",function(e){var t;r&&"input"===e.type?i.$selection.off("input.search input.searchcheck"):(t=e.which)!=l.SHIFT&&t!=l.CTRL&&t!=l.ALT&&t!=l.TAB&&i.handleSearch(e)})},e.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},e.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},e.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.resizeSearch(),n&&this.$search.trigger("focus")},e.prototype.handleSearch=function(){var e;this.resizeSearch(),this._keyUpPrevented||(e=this.$search.val(),this.trigger("query",{term:e})),this._keyUpPrevented=!1},e.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},e.prototype.resizeSearch=function(){this.$search.css("width","25px");var e="100%";""===this.$search.attr("placeholder")&&(e=.75*(this.$search.val().length+1)+"em"),this.$search.css("width",e)},e}),u.define("select2/selection/selectionCss",["../utils"],function(n){function e(){}return e.prototype.render=function(e){var e=e.call(this),t=this.options.get("selectionCssClass")||"";return-1!==t.indexOf(":all:")&&(t=t.replace(":all:",""),n.copyNonInternalCssClasses(e[0],this.$element[0])),e.addClass(t),e},e}),u.define("select2/selection/eventRelay",["jquery"],function(r){function e(){}return e.prototype.bind=function(e,t,n){var i=this,o=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],s=["opening","closing","selecting","unselecting","clearing"];e.call(this,t,n),t.on("*",function(e,t){var n;-1!==o.indexOf(e)&&(n=r.Event("select2:"+e,{params:t=t||{}}),i.$element.trigger(n),-1!==s.indexOf(e))&&(t.prevented=n.isDefaultPrevented())})},e}),u.define("select2/translation",["jquery","require"],function(t,n){function i(e){this.dict=e||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(e){return this.dict[e]},i.prototype.extend=function(e){this.dict=t.extend({},e.all(),this.dict)},i._cache={},i.loadPath=function(e){var t;return e in i._cache||(t=n(e),i._cache[e]=t),new i(i._cache[e])},i}),u.define("select2/diacritics",[],function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ώ":"ω","ς":"σ","’":"'"}}),u.define("select2/data/base",["../utils"],function(n){function i(e,t){i.__super__.constructor.call(this)}return n.Extend(i,n.Observable),i.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},i.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},i.prototype.bind=function(e,t){},i.prototype.destroy=function(){},i.prototype.generateResultId=function(e,t){e=e.id+"-result-";return e+=n.generateChars(4),null!=t.id?e+="-"+t.id.toString():e+="-"+n.generateChars(4),e},i}),u.define("select2/data/select",["./base","../utils","jquery"],function(e,a,l){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return a.Extend(n,e),n.prototype.current=function(e){var t=this;e(Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),function(e){return t.item(l(e))}))},n.prototype.select=function(o){var e,s=this;o.selected=!0,null!=o.element&&"option"===o.element.tagName.toLowerCase()?(o.element.selected=!0,this.$element.trigger("input").trigger("change")):this.$element.prop("multiple")?this.current(function(e){var t=[];(o=[o]).push.apply(o,e);for(var n=0;n<o.length;n++){var i=o[n].id;-1===t.indexOf(i)&&t.push(i)}s.$element.val(t),s.$element.trigger("input").trigger("change")}):(e=o.id,this.$element.val(e),this.$element.trigger("input").trigger("change"))},n.prototype.unselect=function(o){var s=this;this.$element.prop("multiple")&&(o.selected=!1,null!=o.element&&"option"===o.element.tagName.toLowerCase()?(o.element.selected=!1,this.$element.trigger("input").trigger("change")):this.current(function(e){for(var t=[],n=0;n<e.length;n++){var i=e[n].id;i!==o.id&&-1===t.indexOf(i)&&t.push(i)}s.$element.val(t),s.$element.trigger("input").trigger("change")}))},n.prototype.bind=function(e,t){var n=this;(this.container=e).on("select",function(e){n.select(e.data)}),e.on("unselect",function(e){n.unselect(e.data)})},n.prototype.destroy=function(){this.$element.find("*").each(function(){a.RemoveData(this)})},n.prototype.query=function(t,e){var n=[],i=this;this.$element.children().each(function(){var e;"option"!==this.tagName.toLowerCase()&&"optgroup"!==this.tagName.toLowerCase()||(e=l(this),e=i.item(e),null!==(e=i.matches(t,e))&&n.push(e))}),e({results:n})},n.prototype.addOptions=function(e){this.$element.append(e)},n.prototype.option=function(e){e.children?(t=document.createElement("optgroup")).label=e.text:void 0!==(t=document.createElement("option")).textContent?t.textContent=e.text:t.innerText=e.text,void 0!==e.id&&(t.value=e.id),e.disabled&&(t.disabled=!0),e.selected&&(t.selected=!0),e.title&&(t.title=e.title);var t,e=this._normalizeItem(e);return e.element=t,a.StoreData(t,"data",e),l(t)},n.prototype.item=function(e){var t={};if(null==(t=a.GetData(e[0],"data"))){var n=e[0];if("option"===n.tagName.toLowerCase())t={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if("optgroup"===n.tagName.toLowerCase()){for(var t={text:e.prop("label"),children:[],title:e.prop("title")},i=e.children("option"),o=[],s=0;s<i.length;s++){var r=l(i[s]),r=this.item(r);o.push(r)}t.children=o}(t=this._normalizeItem(t)).element=e[0],a.StoreData(e[0],"data",t)}return t},n.prototype._normalizeItem=function(e){e!==Object(e)&&(e={id:e,text:e});return null!=(e=l.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&null!=this.container&&(e._resultId=this.generateResultId(this.container,e)),l.extend({},{selected:!1,disabled:!1},e)},n.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},n}),u.define("select2/data/array",["./select","../utils","jquery"],function(e,t,c){function i(e,t){this._dataToConvert=t.get("data")||[],i.__super__.constructor.call(this,e,t)}return t.Extend(i,e),i.prototype.bind=function(e,t){i.__super__.bind.call(this,e,t),this.addOptions(this.convertToOptions(this._dataToConvert))},i.prototype.select=function(n){var e;0===this.$element.find("option").filter(function(e,t){return t.value==n.id.toString()}).length&&(e=this.option(n),this.addOptions(e)),i.__super__.select.call(this,n)},i.prototype.convertToOptions=function(e){var t=this,n=this.$element.find("option"),i=n.map(function(){return t.item(c(this)).id}).get(),o=[];for(var s=0;s<e.length;s++){var r,a,l=this._normalizeItem(e[s]);0<=i.indexOf(l.id)?(r=n.filter((e=>function(){return c(this).val()==e.id})(l)),a=this.item(r),a=c.extend(!0,{},l,a),a=this.option(a),r.replaceWith(a)):(r=this.option(l),l.children&&(a=this.convertToOptions(l.children),r.append(a)),o.push(r))}return o},i}),u.define("select2/data/ajax",["./array","../utils","jquery"],function(e,t,s){function n(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),n.__super__.constructor.call(this,e,t)}return t.Extend(n,e),n.prototype._applyDefaults=function(e){return s.extend({},{data:function(e){return s.extend({},e,{q:e.term})},transport:function(e,t,n){e=s.ajax(e);return e.then(t),e.fail(n),e}},e,!0)},n.prototype.processResults=function(e){return e},n.prototype.query=function(t,n){var i=this,o=(null!=this._request&&("function"==typeof this._request.abort&&this._request.abort(),this._request=null),s.extend({type:"GET"},this.ajaxOptions));function e(){var e=o.transport(o,function(e){e=i.processResults(e,t);i.options.get("debug")&&window.console&&console.error&&e&&e.results&&Array.isArray(e.results),n(e)},function(){e&&"status"in e&&(0===e.status||"0"===e.status)||i.trigger("results:message",{message:"errorLoading"})});i._request=e}"function"==typeof o.url&&(o.url=o.url.call(this.$element,t)),"function"==typeof o.data&&(o.data=o.data.call(this.$element,t)),this.ajaxOptions.delay&&null!=t.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(e,this.ajaxOptions.delay)):e()},n}),u.define("select2/data/tags",["jquery"],function(t){function e(e,t,n){var i=n.get("tags"),o=n.get("createTag"),o=(void 0!==o&&(this.createTag=o),n.get("insertTag"));if(void 0!==o&&(this.insertTag=o),e.call(this,t,n),Array.isArray(i))for(var s=0;s<i.length;s++){var r=i[s],r=this._normalizeItem(r),r=this.option(r);this.$element.append(r)}}return e.prototype.query=function(e,c,u){var d=this;this._removeOldTags(),null==c.term||null!=c.page?e.call(this,c,u):e.call(this,c,function e(t,n){for(var i=t.results,o=0;o<i.length;o++){var s=i[o],r=null!=s.children&&!e({results:s.children},!0);if((s.text||"").toUpperCase()===(c.term||"").toUpperCase()||r)return!n&&(t.data=i,void u(t))}if(n)return!0;var a,l=d.createTag(c);null!=l&&((a=d.option(l)).attr("data-select2-tag","true"),d.addOptions([a]),d.insertTag(i,l)),t.results=i,u(t)})},e.prototype.createTag=function(e,t){return null==t.term||""===(t=t.term.trim())?null:{id:t,text:t}},e.prototype.insertTag=function(e,t,n){t.unshift(n)},e.prototype._removeOldTags=function(e){this.$element.find("option[data-select2-tag]").each(function(){this.selected||t(this).remove()})},e}),u.define("select2/data/tokenizer",["jquery"],function(c){function e(e,t,n){var i=n.get("tokenizer");void 0!==i&&(this.tokenizer=i),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},e.prototype.query=function(e,t,n){var i=this;t.term=t.term||"";var o=this.tokenizer(t,this.options,function(e){var t=i._normalizeItem(e);i.$element.find("option").filter(function(){return c(this).val()===t.id}).length||((e=i.option(t)).attr("data-select2-tag",!0),i._removeOldTags(),i.addOptions([e])),i.trigger("select",{data:t})});o.term!==t.term&&(this.$search.length&&(this.$search.val(o.term),this.$search.trigger("focus")),t.term=o.term),e.call(this,t,n)},e.prototype.tokenizer=function(e,t,n,i){for(var o=n.get("tokenSeparators")||[],s=t.term,r=0,a=this.createTag||function(e){return{id:e.term,text:e.term}};r<s.length;){var l=s[r];-1===o.indexOf(l)?r++:(l=s.substr(0,r),null==(l=a(c.extend({},t,{term:l})))?r++:(i(l),s=s.substr(r+1)||"",r=0))}return{term:s}},e}),u.define("select2/data/minimumInputLength",[],function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e}),u.define("select2/data/maximumInputLength",[],function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",0<this.maximumInputLength&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e}),u.define("select2/data/maximumSelectionLength",[],function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),t.on("select",function(){i._checkIfMaximumSelected()})},e.prototype.query=function(e,t,n){var i=this;this._checkIfMaximumSelected(function(){e.call(i,t,n)})},e.prototype._checkIfMaximumSelected=function(e,t){var n=this;this.current(function(e){e=null!=e?e.length:0;0<n.maximumSelectionLength&&e>=n.maximumSelectionLength?n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):t&&t()})},e}),u.define("select2/dropdown",["jquery","./utils"],function(t,e){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return e.Extend(n,e.Observable),n.prototype.render=function(){var e=t('<span class="select2-dropdown"><span class="select2-results"></span></span>');return e.attr("dir",this.options.get("dir")),this.$dropdown=e},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n}),u.define("select2/dropdown/search",["jquery"],function(s){function e(){}return e.prototype.render=function(e){var e=e.call(this),t=this.options.get("translations").get("search"),n=s('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=n,this.$search=n.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",t()),e.prepend(n),e},e.prototype.bind=function(e,t,n){var i=this,o=t.id+"-results";e.call(this,t,n),this.$search.on("keydown",function(e){i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented()}),this.$search.on("input",function(e){s(this).off("keyup")}),this.$search.on("keyup input",function(e){i.handleSearch(e)}),t.on("open",function(){i.$search.attr("tabindex",0),i.$search.attr("aria-controls",o),i.$search.trigger("focus"),window.setTimeout(function(){i.$search.trigger("focus")},0)}),t.on("close",function(){i.$search.attr("tabindex",-1),i.$search.removeAttr("aria-controls"),i.$search.removeAttr("aria-activedescendant"),i.$search.val(""),i.$search.trigger("blur")}),t.on("focus",function(){t.isOpen()||i.$search.trigger("focus")}),t.on("results:all",function(e){null!=e.query.term&&""!==e.query.term||(i.showSearch(e)?i.$searchContainer[0].classList.remove("select2-search--hide"):i.$searchContainer[0].classList.add("select2-search--hide"))}),t.on("results:focus",function(e){e.data._resultId?i.$search.attr("aria-activedescendant",e.data._resultId):i.$search.removeAttr("aria-activedescendant")})},e.prototype.handleSearch=function(e){var t;this._keyUpPrevented||(t=this.$search.val(),this.trigger("query",{term:t})),this._keyUpPrevented=!1},e.prototype.showSearch=function(e,t){return!0},e}),u.define("select2/dropdown/hidePlaceholder",[],function(){function e(e,t,n,i){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,i)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return t="string"==typeof t?{id:"",text:t}:t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),i=t.length-1;0<=i;i--){var o=t[i];this.placeholder.id===o.id&&n.splice(i,1)}return n},e}),u.define("select2/dropdown/infiniteScroll",["jquery"],function(n){function e(e,t,n,i){this.lastParams={},e.call(this,t,n,i),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return e.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),t.on("query",function(e){i.lastParams=e,i.loading=!0}),t.on("query:append",function(e){i.lastParams=e,i.loading=!0}),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},e.prototype.loadMoreIfNeeded=function(){var e=n.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&e&&(e=this.$results.offset().top+this.$results.outerHeight(!1),this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)<=e+50)&&this.loadMore()},e.prototype.loadMore=function(){this.loading=!0;var e=n.extend({},{page:1},this.lastParams);e.page++,this.trigger("query:append",e)},e.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},e.prototype.createLoadingMore=function(){var e=n('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),t=this.options.get("translations").get("loadingMore");return e.html(t(this.lastParams)),e},e}),u.define("select2/dropdown/attachBody",["jquery","../utils"],function(u,r){function e(e,t,n){this.$dropdownParent=u(n.get("dropdownParent")||document.body),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),t.on("open",function(){i._showDropdown(),i._attachPositioningHandler(t),i._bindContainerResultHandlers(t)}),t.on("close",function(){i._hideDropdown(),i._detachPositioningHandler(t)}),this.$dropdownContainer.on("mousedown",function(e){e.stopPropagation()})},e.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},e.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t[0].classList.remove("select2"),t[0].classList.add("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},e.prototype.render=function(e){var t=u("<span></span>"),e=e.call(this);return t.append(e),this.$dropdownContainer=t},e.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},e.prototype._bindContainerResultHandlers=function(e,t){var n;this._containerResultsHandlersBound||(n=this,t.on("results:all",function(){n._positionDropdown(),n._resizeDropdown()}),t.on("results:append",function(){n._positionDropdown(),n._resizeDropdown()}),t.on("results:message",function(){n._positionDropdown(),n._resizeDropdown()}),t.on("select",function(){n._positionDropdown(),n._resizeDropdown()}),t.on("unselect",function(){n._positionDropdown(),n._resizeDropdown()}),this._containerResultsHandlersBound=!0)},e.prototype._attachPositioningHandler=function(e,t){var n=this,i="scroll.select2."+t.id,o="resize.select2."+t.id,t="orientationchange.select2."+t.id,s=this.$container.parents().filter(r.hasScroll);s.each(function(){r.StoreData(this,"select2-scroll-position",{x:u(this).scrollLeft(),y:u(this).scrollTop()})}),s.on(i,function(e){var t=r.GetData(this,"select2-scroll-position");u(this).scrollTop(t.y)}),u(window).on(i+" "+o+" "+t,function(e){n._positionDropdown(),n._resizeDropdown()})},e.prototype._detachPositioningHandler=function(e,t){var n="scroll.select2."+t.id,i="resize.select2."+t.id,t="orientationchange.select2."+t.id;this.$container.parents().filter(r.hasScroll).off(n),u(window).off(n+" "+i+" "+t)},e.prototype._positionDropdown=function(){var e=u(window),t=this.$dropdown[0].classList.contains("select2-dropdown--above"),n=this.$dropdown[0].classList.contains("select2-dropdown--below"),i=null,o=this.$container.offset(),s=(o.bottom=o.top+this.$container.outerHeight(!1),{height:this.$container.outerHeight(!1)});s.top=o.top,s.bottom=o.top+s.height;var r=this.$dropdown.outerHeight(!1),a=e.scrollTop(),e=e.scrollTop()+e.height(),a=a<o.top-r,e=e>o.bottom+r,o={left:o.left,top:s.bottom},l=this.$dropdownParent,c=("static"===l.css("position")&&(l=l.offsetParent()),{top:0,left:0});(u.contains(document.body,l[0])||l[0].isConnected)&&(c=l.offset()),o.top-=c.top,o.left-=c.left,t||n||(i="below"),e||!a||t?!a&&e&&t&&(i="below"):i="above",("above"==i||t&&"below"!==i)&&(o.top=s.top-c.top-r),null!=i&&(this.$dropdown[0].classList.remove("select2-dropdown--below"),this.$dropdown[0].classList.remove("select2-dropdown--above"),this.$dropdown[0].classList.add("select2-dropdown--"+i),this.$container[0].classList.remove("select2-container--below"),this.$container[0].classList.remove("select2-container--above"),this.$container[0].classList.add("select2-container--"+i)),this.$dropdownContainer.css(o)},e.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},e.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},e}),u.define("select2/dropdown/minimumResultsForSearch",[],function(){function e(e,t,n,i){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,i)}return e.prototype.showSearch=function(e,t){return!(function e(t){for(var n=0,i=0;i<t.length;i++){var o=t[i];o.children?n+=e(o.children):n++}return n}(t.data.results)<this.minimumResultsForSearch)&&e.call(this,t)},e}),u.define("select2/dropdown/selectOnClose",["../utils"],function(n){function e(){}return e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),t.on("close",function(e){i._handleSelectOnClose(e)})},e.prototype._handleSelectOnClose=function(e,t){if(t&&null!=t.originalSelect2Event){t=t.originalSelect2Event;if("select"===t._type||"unselect"===t._type)return}var t=this.getHighlightedResults();t.length<1||null!=(t=n.GetData(t[0],"data")).element&&t.element.selected||null==t.element&&t.selected||this.trigger("select",{data:t})},e}),u.define("select2/dropdown/closeOnSelect",[],function(){function e(){}return e.prototype.bind=function(e,t,n){var i=this;e.call(this,t,n),t.on("select",function(e){i._selectTriggered(e)}),t.on("unselect",function(e){i._selectTriggered(e)})},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&(n.ctrlKey||n.metaKey)||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e}),u.define("select2/dropdown/dropdownCss",["../utils"],function(n){function e(){}return e.prototype.render=function(e){var e=e.call(this),t=this.options.get("dropdownCssClass")||"";return-1!==t.indexOf(":all:")&&(t=t.replace(":all:",""),n.copyNonInternalCssClasses(e[0],this.$element[0])),e.addClass(t),e},e}),u.define("select2/dropdown/tagsSearchHighlight",["../utils"],function(i){function e(){}return e.prototype.highlightFirstItem=function(e){var t=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)");if(0<t.length){var t=t.first(),n=i.GetData(t[0],"data").element;if(n&&n.getAttribute&&"true"===n.getAttribute("data-select2-tag"))return void t.trigger("mouseenter")}e.call(this)},e}),u.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var e=e.input.length-e.maximum,t="Please delete "+e+" character";return 1!=e&&(t+="s"),t},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}}),u.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],function(l,s,r,a,c,u,d,p,h,f,g,t,m,v,y,w,b,_,x,$,A,S,C,E,D,O,k,L,j,T,e){function n(){this.reset()}return n.prototype.apply=function(e){null==(e=l.extend(!0,{},this.defaults,e)).dataAdapter&&(null!=e.ajax?e.dataAdapter=y:null!=e.data?e.dataAdapter=v:e.dataAdapter=m,0<e.minimumInputLength&&(e.dataAdapter=f.Decorate(e.dataAdapter,_)),0<e.maximumInputLength&&(e.dataAdapter=f.Decorate(e.dataAdapter,x)),0<e.maximumSelectionLength&&(e.dataAdapter=f.Decorate(e.dataAdapter,$)),e.tags&&(e.dataAdapter=f.Decorate(e.dataAdapter,w)),null==e.tokenSeparators&&null==e.tokenizer||(e.dataAdapter=f.Decorate(e.dataAdapter,b))),null==e.resultsAdapter&&(e.resultsAdapter=s,null!=e.ajax&&(e.resultsAdapter=f.Decorate(e.resultsAdapter,E)),null!=e.placeholder&&(e.resultsAdapter=f.Decorate(e.resultsAdapter,C)),e.selectOnClose&&(e.resultsAdapter=f.Decorate(e.resultsAdapter,k)),e.tags)&&(e.resultsAdapter=f.Decorate(e.resultsAdapter,T)),null==e.dropdownAdapter&&(e.multiple?e.dropdownAdapter=A:(t=f.Decorate(A,S),e.dropdownAdapter=t),0!==e.minimumResultsForSearch&&(e.dropdownAdapter=f.Decorate(e.dropdownAdapter,O)),e.closeOnSelect&&(e.dropdownAdapter=f.Decorate(e.dropdownAdapter,L)),null!=e.dropdownCssClass&&(e.dropdownAdapter=f.Decorate(e.dropdownAdapter,j)),e.dropdownAdapter=f.Decorate(e.dropdownAdapter,D)),null==e.selectionAdapter&&(e.multiple?e.selectionAdapter=a:e.selectionAdapter=r,null!=e.placeholder&&(e.selectionAdapter=f.Decorate(e.selectionAdapter,c)),e.allowClear&&(e.selectionAdapter=f.Decorate(e.selectionAdapter,u)),e.multiple&&(e.selectionAdapter=f.Decorate(e.selectionAdapter,d)),null!=e.selectionCssClass&&(e.selectionAdapter=f.Decorate(e.selectionAdapter,p)),e.selectionAdapter=f.Decorate(e.selectionAdapter,h)),e.language=this._resolveLanguage(e.language),e.language.push("en");for(var t,n=[],i=0;i<e.language.length;i++){var o=e.language[i];-1===n.indexOf(o)&&n.push(o)}return e.language=n,e.translations=this._processTranslations(e.language,e.debug),e},n.prototype.reset=function(){function a(e){return e.replace(/[^\u0000-\u007E]/g,function(e){return t[e]||e})}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:f.escapeMarkup,language:{},matcher:function e(t,n){if(null==t.term||""===t.term.trim())return n;if(n.children&&0<n.children.length){for(var i=l.extend(!0,{},n),o=n.children.length-1;0<=o;o--)null==e(t,n.children[o])&&i.children.splice(o,1);return 0<i.children.length?i:e(t,i)}var s=a(n.text).toUpperCase(),r=a(t.term).toUpperCase();return-1<s.indexOf(r)?n:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},n.prototype.applyFromElement=function(e,t){var n=e.language,i=this.defaults.language,o=t.prop("lang"),t=t.closest("[lang]").prop("lang"),o=Array.prototype.concat.call(this._resolveLanguage(o),this._resolveLanguage(n),this._resolveLanguage(i),this._resolveLanguage(t));return e.language=o,e},n.prototype._resolveLanguage=function(e){if(!e)return[];if(l.isEmptyObject(e))return[];if(l.isPlainObject(e))return[e];for(var t,n=Array.isArray(e)?e:[e],i=[],o=0;o<n.length;o++)i.push(n[o]),"string"==typeof n[o]&&0<n[o].indexOf("-")&&(t=n[o].split("-")[0],i.push(t));return i},n.prototype._processTranslations=function(e,t){for(var n=new g,i=0;i<e.length;i++){var o=new g,s=e[i];if("string"==typeof s)try{o=g.loadPath(s)}catch(e){try{s=this.defaults.amdLanguageBase+s,o=g.loadPath(s)}catch(e){t&&window.console&&console.warn}}else o=l.isPlainObject(s)?new g(s):s;n.extend(o)}return n},n.prototype.set=function(e,t){var n={},e=(n[l.camelCase(e)]=t,f._convertData(n));l.extend(!0,this.defaults,e)},new n}),u.define("select2/options",["jquery","./defaults","./utils"],function(c,n,u){function e(e,t){this.options=e,null!=t&&this.fromElement(t),null!=t&&(this.options=n.applyFromElement(this.options,t)),this.options=n.apply(this.options)}return e.prototype.fromElement=function(e){var t=["select2"],n=(null==this.options.multiple&&(this.options.multiple=e.prop("multiple")),null==this.options.disabled&&(this.options.disabled=e.prop("disabled")),null==this.options.autocomplete&&e.prop("autocomplete")&&(this.options.autocomplete=e.prop("autocomplete")),null==this.options.dir&&(e.prop("dir")?this.options.dir=e.prop("dir"):e.closest("[dir]").prop("dir")?this.options.dir=e.closest("[dir]").prop("dir"):this.options.dir="ltr"),e.prop("disabled",this.options.disabled),e.prop("multiple",this.options.multiple),u.GetData(e[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn,u.StoreData(e[0],"data",u.GetData(e[0],"select2Tags")),u.StoreData(e[0],"tags",!0)),u.GetData(e[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn,e.attr("ajax--url",u.GetData(e[0],"ajaxUrl")),u.StoreData(e[0],"ajax-Url",u.GetData(e[0],"ajaxUrl"))),{});function i(e,t){return t.toUpperCase()}for(var o=0;o<e[0].attributes.length;o++){var s=e[0].attributes[o].name,r="data-";s.substr(0,r.length)==r&&(s=s.substring(r.length),r=u.GetData(e[0],s),n[s.replace(/-([a-z])/g,i)]=r)}c.fn.jquery&&"1."==c.fn.jquery.substr(0,2)&&e[0].dataset&&(n=c.extend(!0,{},e[0].dataset,n));var a,l=c.extend(!0,{},u.GetData(e[0]),n);for(a in l=u._convertData(l))-1<t.indexOf(a)||(c.isPlainObject(this.options[a])?c.extend(this.options[a],l[a]):this.options[a]=l[a]);return this},e.prototype.get=function(e){return this.options[e]},e.prototype.set=function(e,t){this.options[e]=t},e}),u.define("select2/core",["jquery","./options","./utils","./keys"],function(t,o,s,i){function r(e,t){null!=s.GetData(e[0],"select2")&&s.GetData(e[0],"select2").destroy(),this.$element=e,this.id=this._generateId(e),this.options=new o(t=t||{},e),r.__super__.constructor.call(this);var t=e.attr("tabindex")||0,t=(s.StoreData(e[0],"old-tabindex",t),e.attr("tabindex","-1"),this.options.get("dataAdapter")),t=(this.dataAdapter=new t(e,this.options),this.render()),n=(this._placeContainer(t),this.options.get("selectionAdapter")),n=(this.selection=new n(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,t),this.options.get("dropdownAdapter")),n=(this.dropdown=new n(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,t),this.options.get("resultsAdapter")),i=(this.results=new n(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown),this);this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current(function(e){i.trigger("selection:update",{data:e})}),e[0].classList.add("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),s.StoreData(e[0],"select2",this),e.data("select2",this)}return s.Extend(r,s.Observable),r.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+s.generateChars(2):s.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},r.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},r.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t)return null!=(i=this._resolveWidth(e,"style"))?i:this._resolveWidth(e,"element");if("element"==t)return(i=e.outerWidth(!1))<=0?"auto":i+"px";if("style"!=t)return"computedstyle"==t?window.getComputedStyle(e[0]).width:t;var i=e.attr("style");if("string"==typeof i)for(var o=i.split(";"),s=0,r=o.length;s<r;s+=1){var a=o[s].replace(/\s/g,"").match(n);if(null!==a&&1<=a.length)return a[1]}return null},r.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},r.prototype._registerDomEvents=function(){var t=this;this.$element.on("change.select2",function(){t.dataAdapter.current(function(e){t.trigger("selection:update",{data:e})})}),this.$element.on("focus.select2",function(e){t.trigger("focus",e)}),this._syncA=s.bind(this._syncAttributes,this),this._syncS=s.bind(this._syncSubtree,this),this._observer=new window.MutationObserver(function(e){t._syncA(),t._syncS(e)}),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},r.prototype._registerDataEvents=function(){var n=this;this.dataAdapter.on("*",function(e,t){n.trigger(e,t)})},r.prototype._registerSelectionEvents=function(){var n=this,i=["toggle","focus"];this.selection.on("toggle",function(){n.toggleDropdown()}),this.selection.on("focus",function(e){n.focus(e)}),this.selection.on("*",function(e,t){-1===i.indexOf(e)&&n.trigger(e,t)})},r.prototype._registerDropdownEvents=function(){var n=this;this.dropdown.on("*",function(e,t){n.trigger(e,t)})},r.prototype._registerResultsEvents=function(){var n=this;this.results.on("*",function(e,t){n.trigger(e,t)})},r.prototype._registerEvents=function(){var n=this;this.on("open",function(){n.$container[0].classList.add("select2-container--open")}),this.on("close",function(){n.$container[0].classList.remove("select2-container--open")}),this.on("enable",function(){n.$container[0].classList.remove("select2-container--disabled")}),this.on("disable",function(){n.$container[0].classList.add("select2-container--disabled")}),this.on("blur",function(){n.$container[0].classList.remove("select2-container--focus")}),this.on("query",function(t){n.isOpen()||n.trigger("open",{}),this.dataAdapter.query(t,function(e){n.trigger("results:all",{data:e,query:t})})}),this.on("query:append",function(t){this.dataAdapter.query(t,function(e){n.trigger("results:append",{data:e,query:t})})}),this.on("keypress",function(e){var t=e.which;n.isOpen()?t===i.ESC||t===i.UP&&e.altKey?(n.close(e),e.preventDefault()):t===i.ENTER||t===i.TAB?(n.trigger("results:select",{}),e.preventDefault()):t===i.SPACE&&e.ctrlKey?(n.trigger("results:toggle",{}),e.preventDefault()):t===i.UP?(n.trigger("results:previous",{}),e.preventDefault()):t===i.DOWN&&(n.trigger("results:next",{}),e.preventDefault()):(t===i.ENTER||t===i.SPACE||t===i.DOWN&&e.altKey)&&(n.open(),e.preventDefault())})},r.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},r.prototype._isChangeMutation=function(e){var t=this;if(e.addedNodes&&0<e.addedNodes.length){for(var n=0;n<e.addedNodes.length;n++)if(e.addedNodes[n].selected)return!0}else{if(e.removedNodes&&0<e.removedNodes.length)return!0;if(Array.isArray(e))return e.some(function(e){return t._isChangeMutation(e)})}return!1},r.prototype._syncSubtree=function(e){var e=this._isChangeMutation(e),t=this;e&&this.dataAdapter.current(function(e){t.trigger("selection:update",{data:e})})},r.prototype.trigger=function(e,t){var n=r.__super__.trigger,i={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"};if(void 0===t&&(t={}),e in i){var o={prevented:!1,name:e,args:t};if(n.call(this,i[e],o),o.prevented)return void(t.prevented=!0)}n.call(this,e,t)},r.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},r.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},r.prototype.close=function(e){this.isOpen()&&this.trigger("close",{originalEvent:e})},r.prototype.isEnabled=function(){return!this.isDisabled()},r.prototype.isDisabled=function(){return this.options.get("disabled")},r.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},r.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},r.prototype.focus=function(e){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},r.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn;e=!(e=null!=e&&0!==e.length?e:[!0])[0];this.$element.prop("disabled",e)},r.prototype.data=function(){this.options.get("debug")&&0<arguments.length&&window.console&&console.warn;var t=[];return this.dataAdapter.current(function(e){t=e}),t},r.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn,null==e||0===e.length)return this.$element.val();e=e[0];Array.isArray(e)&&(e=e.map(function(e){return e.toString()})),this.$element.val(e).trigger("input").trigger("change")},r.prototype.destroy=function(){s.RemoveData(this.$container[0]),this.$container.remove(),this._observer.disconnect(),this._observer=null,this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",s.GetData(this.$element[0],"old-tabindex")),this.$element[0].classList.remove("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),s.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},r.prototype.render=function(){var e=t('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return e.attr("dir",this.options.get("dir")),this.$container=e,this.$container[0].classList.add("select2-container--"+this.options.get("theme")),s.StoreData(e[0],"element",this.$element),e},r}),u.define("select2/dropdown/attachContainer",[],function(){function e(e,t,n){e.call(this,t,n)}return e.prototype.position=function(e,t,n){n.find(".dropdown-wrapper").append(t),t[0].classList.add("select2-dropdown--below"),n[0].classList.add("select2-container--below")},e}),u.define("select2/dropdown/stopPropagation",[],function(){function e(){}return e.prototype.bind=function(e,t,n){e.call(this,t,n);this.$dropdown.on(["blur","change","click","dblclick","focus","focusin","focusout","input","keydown","keyup","keypress","mousedown","mouseenter","mouseleave","mousemove","mouseover","mouseup","search","touchend","touchstart"].join(" "),function(e){e.stopPropagation()})},e}),u.define("select2/selection/stopPropagation",[],function(){function e(){}return e.prototype.bind=function(e,t,n){e.call(this,t,n);this.$selection.on(["blur","change","click","dblclick","focus","focusin","focusout","input","keydown","keyup","keypress","mousedown","mouseenter","mouseleave","mousemove","mouseover","mouseup","search","touchend","touchstart"].join(" "),function(e){e.stopPropagation()})},e}),o=function(c){var u,d,e=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],t="onwheel"in document||9<=document.documentMode?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],p=Array.prototype.slice;if(c.event.fixHooks)for(var n=e.length;n;)c.event.fixHooks[e[--n]]=c.event.mouseHooks;var h=c.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var e=t.length;e;)this.addEventListener(t[--e],i,!1);else this.onmousewheel=i;c.data(this,"mousewheel-line-height",h.getLineHeight(this)),c.data(this,"mousewheel-page-height",h.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var e=t.length;e;)this.removeEventListener(t[--e],i,!1);else this.onmousewheel=null;c.removeData(this,"mousewheel-line-height"),c.removeData(this,"mousewheel-page-height")},getLineHeight:function(e){var e=c(e),t=e["offsetParent"in c.fn?"offsetParent":"parent"]();return t.length||(t=c("body")),parseInt(t.css("fontSize"),10)||parseInt(e.css("fontSize"),10)||16},getPageHeight:function(e){return c(e).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};function i(e){var t,n=e||window.event,i=p.call(arguments,1),o=0,s=0,r=0,a=0,l=0;if((e=c.event.fix(n)).type="mousewheel","detail"in n&&(r=-1*n.detail),"wheelDelta"in n&&(r=n.wheelDelta),"wheelDeltaY"in n&&(r=n.wheelDeltaY),"wheelDeltaX"in n&&(s=-1*n.wheelDeltaX),"axis"in n&&n.axis===n.HORIZONTAL_AXIS&&(s=-1*r,r=0),o=0===r?s:r,"deltaY"in n&&(o=r=-1*n.deltaY),"deltaX"in n&&(s=n.deltaX,0===r)&&(o=-1*s),0!==r||0!==s)return 1===n.deltaMode?(o*=t=c.data(this,"mousewheel-line-height"),r*=t,s*=t):2===n.deltaMode&&(o*=t=c.data(this,"mousewheel-page-height"),r*=t,s*=t),t=Math.max(Math.abs(r),Math.abs(s)),(!d||t<d)&&g(n,d=t)&&(d/=40),g(n,t)&&(o/=40,s/=40,r/=40),o=Math[1<=o?"floor":"ceil"](o/d),s=Math[1<=s?"floor":"ceil"](s/d),r=Math[1<=r?"floor":"ceil"](r/d),h.settings.normalizeOffset&&this.getBoundingClientRect&&(n=this.getBoundingClientRect(),a=e.clientX-n.left,l=e.clientY-n.top),e.deltaX=s,e.deltaY=r,e.deltaFactor=d,e.offsetX=a,e.offsetY=l,e.deltaMode=0,i.unshift(e,o,s,r),u&&clearTimeout(u),u=setTimeout(f,200),(c.event.dispatch||c.event.handle).apply(this,i)}function f(){d=null}function g(e,t){return h.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120==0}c.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})},"function"==typeof u.define&&u.define.amd?u.define("jquery-mousewheel",["jquery"],o):"object"==typeof exports?module.exports=o:o(t),u.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],function(o,e,s,t,r){var a;return null==o.fn.awsmSelect2&&(a=["open","close","destroy"],o.fn.awsmSelect2=function(t){if("object"==typeof(t=t||{}))return this.each(function(){var e=o.extend(!0,{},t);new s(o(this),e)}),this;var n,i;if("string"==typeof t)return i=Array.prototype.slice.call(arguments,1),this.each(function(){var e=r.GetData(this,"select2");null==e&&window.console&&console.error,n=e[t].apply(e,i)}),-1<a.indexOf(t)?this:n;throw new Error("Invalid arguments for Select2: "+t)}),null!=o.fn.select2&&null!=o.fn.select2.defaults&&(o.fn.awsmSelect2.defaults=o.fn.select2.defaults),null==o.fn.awsmSelect2.defaults&&(o.fn.awsmSelect2.defaults=t),s});var e,p,s,r,h,f,g,m,v,y,n,i,w,o,a={define:u.define,require:u.require};function b(e,t){return n.call(e,t)}function l(e,t){var n,i,o,s,r,a,l,c,u,d,p=t&&t.split("/"),h=v.map,f=h&&h["*"]||{};if(e){for(t=(e=e.split("/")).length-1,v.nodeIdCompat&&w.test(e[t])&&(e[t]=e[t].replace(w,"")),"."===e[0].charAt(0)&&p&&(e=p.slice(0,p.length-1).concat(e)),c=0;c<e.length;c++)"."===(d=e[c])?(e.splice(c,1),--c):".."!==d||0===c||1===c&&".."===e[2]||".."===e[c-1]||0<c&&(e.splice(c-1,2),c-=2);e=e.join("/")}if((p||f)&&h){for(c=(n=e.split("/")).length;0<c;--c){if(i=n.slice(0,c).join("/"),p)for(u=p.length;0<u;--u)if(o=(o=h[p.slice(0,u).join("/")])&&o[i]){s=o,r=c;break}if(s)break;!a&&f&&f[i]&&(a=f[i],l=c)}!s&&a&&(s=a,r=l),s&&(n.splice(0,r,s),e=n.join("/"))}return e}function _(t,n){return function(){var e=i.call(arguments,0);return"string"!=typeof e[0]&&1===e.length&&e.push(null),r.apply(p,e.concat([t,n]))}}function x(e){var t;if(b(m,e)&&(t=m[e],delete m[e],y[e]=!0,s.apply(p,t)),b(g,e)||b(y,e))return g[e];throw new Error("No "+e)}function c(e){var t,n=e?e.indexOf("!"):-1;return-1<n&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function A(e){return e?c(e):[]}var u=a.require("jquery.select2");return t.fn.awsmSelect2.amd=a,u}),jQuery(window).on("load",function(){jQuery(".awsm-job-setup").addClass("loaded")}),jQuery(document).ready(function(s){var r=window.awsmJobsAdminMain=window.awsmJobsAdminMain||{},e=(r.selectControl=function(e,t){var n={minimumResultsForSearch:25,theme:"awsm-job"};0<(t=void 0!==t?t:"").length&&(n.placeholder=t),e.awsmSelect2(n)},r.tagSelect=function(e,t,n){t=void 0===t||t,n=void 0!==n?n:{},0<e.length&&(t={tags:!0,tokenSeparators:[","],theme:"awsm-job",dropdownCssClass:t?"awsm-hidden-control":"awsm-select2-dropdown-control"},jQuery.extend(t,n),e.awsmSelect2(t))},s(".awsm-check-control-field").on("change",function(){var e=s(this),t=e.data("reqTarget");void 0!==t&&(t=s(t),e.is(":checked")?(t.focus(),t.prop("required",!0)):t.removeAttr("required"))}),s(".awsm-check-toggle-control").on("change",function(){var e=s(this),t=e.data("toggleTarget");void 0!==t&&(t=s(t),e.is(":checked")&&void 0!==(e=e.data("toggle"))&&0!=e?t.removeClass("awsm-hide"):t.addClass("awsm-hide"))}),s(".awsm-jobs-colorpicker-field").wpColorPicker(),r.selectControl(s(".awsm-select-page-control"),awsmJobsAdmin.i18n.select2_no_page),r.selectControl(s(".awsm-select-control")),new Date);s("#awsm-jobs-datepicker").datepicker({altField:"#awsm-jobs-datepicker-alt",altFormat:"yy-mm-dd",showOn:"both",buttonText:"",buttonImage:awsmJobsAdmin.plugin_url+"/assets/img/calendar-alt.svg",buttonImageOnly:!0,changeMonth:!0,numberOfMonths:1,minDate:e}),r.tagSelect(s(".awsm_jobs_filter_tags"),!0,{matcher:function(e,t){return""===e.term.trim()?t:void 0!==t.id&&t.id.toLowerCase()===e.term.toLowerCase()?s.extend({},t,!0):null},templateResult:function(e){return e.id}}),r.tagSelect(s(".awsm_job_specification_terms"),!1,{createTag:function(e){var t=s.trim(e.term);return""===t?null:{id:t=!_.isNaN(t)&&0<t.length?"awsm-term-id-"+t:t,text:e.term,newItem:!0}}});var i=new RegExp("^([a-z0-9]+(-|_))*[a-z0-9]+$"),n=s("#awsm-job-specifications-options-container"),o={"а":"a","А":"a","б":"b","Б":"B","в":"v","В":"V","ґ":"g","г":"g","Г":"G","д":"d","Д":"D","е":"e","Е":"E","є":"ye","э":"e","Э":"E","и":"i","і":"i","ї":"yi","й":"i","И":"I","Й":"I","к":"k","К":"K","л":"l","Л":"L","м":"m","М":"M","н":"n","Н":"N","о":"o","О":"O","п":"p","П":"P","р":"r","Р":"R","с":"s","С":"S","т":"t","Т":"T","у":"u","У":"U","ф":"f","Ф":"F","х":"h","Х":"H","ц":"c","ч":"ch","Ч":"CH","ш":"sh","Ш":"SH","щ":"sch","Щ":"SCH","ж":"zh","Ж":"ZH","з":"z","З":"Z","Ъ":"'","ь":"'","ъ":"'","Ь":"'","ы":"i","Ы":"I","ю":"yu","Ю":"YU","я":"ya","Я":"Ya","ё":"yo","Ё":"YO","Ц":"TS"},a=[{id:"",text:""}];function l(e){return e.id?s('<span><i class="awsm-job-icon-'+e.id+'"></i> '+e.id+"</span>"):e.text}function c(e,t){t=void 0!==t&&t;var n,i=e.data("target"),i=s(i);0<i.length&&(n=e.closest(".awsm-admin-settings"),i.find('[data-required="required"]').prop("required",!0),n.find(".awsm-sub-options-container").hide(),n.find(".awsm-nav-subtab").removeClass("current"),e.addClass("current"),t?i.fadeIn():i.show())}r.iconSelect=function(e,t){var n=e.data("placeholder");e.awsmSelect2({placeholder:{id:"",text:n},allowClear:!0,data:t,templateResult:l,templateSelection:l,theme:"awsm-job"})},s.getJSON(awsmJobsAdmin.plugin_url+"/assets/fonts/awsm-icons.json",function(e){s.each(e.icons,function(e,t){a.push({id:t,text:t})}),r.iconSelect(s(".awsm-icon-select-control"),a)}),s("#awsm-repeatable-specifications").sortable({items:".awsm-job-specifications-settings-row",axis:"y",handle:".awsm-specs-drag-control",cursor:"grabbing"}),s(".awsm_jobs_filter_tags").on("select2:unselect",function(e){var t=s(this).parents(".awsm-job-specifications-settings-row"),n=t.data("index"),e=e.params.data.element,e=s(e).data("termid");void 0!==n&&_.isNumber(e)&&t.append('<input type="hidden" class="awsm_jobs_remove_filter_tags" name="awsm_jobs_filter['+n+'][remove_tags][]" value="'+e+'" />')}),s(".awsm-add-filter-row").on("click",function(e){e.preventDefault();var t,n,i,o=!0;s(".awsm-job-specifications-settings-row .awsm-jobs-spec-title").each(function(){0==s(this).val().length&&(s(this).focus(),o=!1)}),o&&(t=(e=s("#awsm-repeatable-specifications")).data("next"),n=wp.template("awsm-job-spec-settings"),i={index:t},e.data("next",t+1),e.find(".awsm_job_specifications_settings_body").append(n(i)),r.tagSelect(s(".awsm_jobs_filter_tags").last()),r.iconSelect(s(".awsm-icon-select-control").last(),a))}),s("#awsm-repeatable-specifications").on("click",".awsm-filters-remove-row",function(e){e.preventDefault();var e=s(this),t=s("#awsm-repeatable-specifications"),n=".awsm-job-specifications-settings-row",i=s(n).length,o=e.data("taxonomy");t.data("next",void 0!==i&&0<i?i-1:0),e.parents(n).remove(),void 0!==o&&t.append('<input type="hidden" name="awsm_jobs_remove_filters[]" value="'+o+'" />')}),n.on("keyup blur",".awsm-jobs-spec-title",function(){var e,t=s(this),n=t.val(),t=t.parents(".awsm-job-specifications-settings-row");0<n.length&&(n=s.trim(n).replace(/\s+/g,"-").toLowerCase(),i.test(n)||(n=(e=n.split("").map(function(e){return e in o?o[e]:e}).join(""))!==n?e:""),t.find(".awsm-jobs-spec-key").val(n))}),n.parents("#settings-awsm-settings-specifications").find("form").submit(function(e){var t;n.is(":visible")&&(t=!0,s(".awsm-jobs-error-container").remove(),s(".awsm-jobs-spec-key").each(function(){var e=s(this).val();i.test(e)||(t=!1)}),t||(e.preventDefault(),e=wp.template("awsm-job-spec-settings-error"),n.find(".awsm-form-section").append(e({isInvalidKey:!0}))))}),s('#awsm-job-settings-wrap input[type="submit"]').on("click",function(){!1===s("#awsm-job-settings-wrap form").get(0).checkValidity()?(s(".awsm-jobs-settings-error").removeClass("awsm-hide"),s(".awsm-acc-head").addClass("on"),s(".awsm-acc-content").slideDown("normal"),s("html, body").animate({scrollTop:s("#awsm-job-settings-wrap .awsm-settings-tab-wrapper").offset().top},600)):s(".awsm-jobs-settings-error").addClass("awsm-hide")});var u,d=".awsm_current_settings_subtab",e=s(d),p=(0<e.length&&s(e).each(function(){var e=s(this).val();c(s("#"+e),!0)}),s("#awsm-job-settings-wrap").on("click",".awsm-nav-subtab",function(e){e.preventDefault();var e=s(this),t=e.attr("id"),n=e.closest(".awsm-admin-settings");e.hasClass("current")||(n.find('[data-required="required"]').prop("required",!1),c(e,!0),n.find(d).val(t))}),s(".awsm-jobs-settings-loader-container").fadeOut(function(){s("#awsm-jobs-settings-section").css("visibility","visible").addClass("awsm-visible")}),awsmJobsAdmin.i18n.image_upload);s("#awsm-job-settings-wrap").on("click",".awsm-settings-image-upload-button",function(e){e.preventDefault();var i=s(this),e=(i.parent(".awsm-settings-image-field-container").addClass("awsm-settings-image-trigger-active"),u=u||wp.media({title:p.title,multiple:!1,library:{type:"image"},button:{text:p.btn_text}}),wp.media.view.Button);wp.media.view.Button=e.extend({initialize:function(){var e=_.defaults(this.options,this.defaults);this.model=new Backbone.Model(e),this.listenTo(this.model,"change",this.render)}}),u.on("select",function(){var e,t,n=u.state().get("selection").first().toJSON();"image"===n.type&&(e=n.url,(t=i.parent(".awsm-settings-image-field-container.awsm-settings-image-trigger-active")).find(".awsm-settings-image").removeClass("awsm-settings-no-image").html('<img src="'+e+'" />'),t.find(".awsm-settings-image-remove-button").removeClass("awsm-hidden-control"),t.find(".awsm-settings-image-upload-button").text(p.change),t.find(".awsm-settings-image-field").val(n.id),t.removeClass("awsm-settings-image-trigger-active"))}),u.open()}),s("#awsm-job-settings-wrap").on("click",".awsm-settings-image-remove-button",function(e){e.preventDefault();var e=s(this),t=e.parent(".awsm-settings-image-field-container");t.find(".awsm-settings-image").addClass("awsm-settings-no-image").html("<span>"+p.no_image+"</span>"),t.find(".awsm-settings-image-upload-button").text(p.select),t.find(".awsm-settings-image-field").val(""),e.addClass("awsm-hidden-control")}),s("#awsm-jobs-settings-section").on("click",".awsm-acc-head",function(e){var t=!0,n=s(this),i=s(".awsm-toggle-switch");(t=0<i.length&&(i.is(e.target)||0<i.has(e.target).length)?!1:t)&&(s(".awsm-acc-head").removeClass("on"),s(".awsm-acc-content").slideUp("normal"),1==n.next(".awsm-acc-content").is(":hidden"))&&(n.addClass("on"),n.next(".awsm-acc-content").slideDown("normal"))}),s(".awsm-settings-switch").on("change",function(){var e=s(this),t=e.attr("id"),n=e.val(),e=(e.is(":checked")||(n=""),{action:"settings_switch",nonce:awsmJobsAdmin.nonce,option:t,option_value:n});s.ajax({url:awsmJobsAdmin.ajaxurl,data:e,type:"POST"}).fail(function(e){})}),0<s("#awsm-copy-clip").length&&((e=new Clipboard("#awsm-copy-clip")).on("success",function(e){e.clearSelection(),e.trigger.textContent="Copied",window.setTimeout(function(){e.trigger.textContent="Copy"},2e3)}),e.on("error",function(e){e.trigger.textContent='Press "Ctrl + C" to copy',window.setTimeout(function(){e.trigger.textContent="Copy"},2e3)})),s("#awsm-copy-clip").on("click",function(e){e.preventDefault()}),s(".awsm-job-plugin-rating-action").on("click",function(e){e.preventDefault();var e=s(this),t=e.data("status"),e=e.data("context"),e={nonce:awsmJobsAdmin.nonce,action:"awsm_plugin_rating",context:e,status:t};s.ajax({url:awsmJobsAdmin.ajaxurl,data:e,type:"POST"}).done(function(e){e&&"success"===e.code&&s(".awsm-job-plugin-rating-wrapper").slideUp("fast")})}),s("#awsm-job-setup-form").on("submit",function(e){e.preventDefault(),s("#awsm-jobs-setup-btn").prop("disabled",!0),s(".awsm-job-setup-notice").addClass("awsm-hide");e=s("#awsm-job-setup-form").serializeArray();s.ajax({url:awsmJobsAdmin.ajaxurl,data:e,type:"POST"}).done(function(e){var n;void 0!==e.redirect&&e.redirect?window.location.replace(e.redirect):(n="",s(e.error).each(function(e,t){n+="<p>"+t+"</p>"}),n.length&&s(".awsm-job-setup-notice").html(n).removeClass("awsm-hide"))}).always(function(){s("#awsm-jobs-setup-btn").prop("disabled",!1)})})});