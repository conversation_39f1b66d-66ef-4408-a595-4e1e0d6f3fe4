!function(){"use strict";jQuery((function(a){var e=".awsm-b-job-wrap",s=".awsm-b-job-listings",t=".awsm-b-filter-wrap",n=window.location.protocol+"//"+window.location.host+window.location.pathname,i=!0;function o(e){var s=[],t=["listings","specs","search","lang","taxonomy","termId"];t.push("awsm-layout"),t.push("awsm-hide-expired-jobs"),t.push("awsm-other-options"),a(document).trigger("awsmJobBlockListingsData",[t]);var n=e.data();return a.each(n,(function(e,n){-1===a.inArray(e,t)&&s.push({name:e,value:n})})),s}function r(e){var n=e.find(s),r=e.find(t+" form"),l=r.serializeArray(),d=n.data("listings"),m=n.data("specs"),c=n.data("awsm-layout"),p=n.data("awsm-hide-expired-jobs"),b=n.data("awsm-other-options");l.push({name:"listings_per_page",value:d}),void 0!==m&&l.push({name:"shortcode_specs",value:m}),void 0!==c&&l.push({name:"awsm-layout",value:c}),void 0!==p&&l.push({name:"awsm-hide-expired-jobs",value:p}),void 0!==b&&l.push({name:"awsm-other-options",value:b});var u=o(n);u.length>0&&(l=l.concat(u)),a(document).trigger("awsmJobBlockFiltersFormData",[n,l]),i&&(i=!1,a.ajax({url:r.attr("action"),beforeSend:function(){n.addClass("awsm-b-jobs-loading")},data:l,type:r.attr("method")}).done((function(s){n.html(s);var t=e.find(".awsm-b-job-search");t.length>0&&(t.val().length>0?(e.find(".awsm-b-job-search-btn").addClass("awsm-b-job-hide"),e.find(".awsm-b-job-search-close-btn").removeClass("awsm-b-job-hide")):e.find(".awsm-b-job-search-btn").removeClass("awsm-b-job-hide")),a(document).trigger("awsmjobs_filtered_listings",[e,s])})).fail((function(a){console.log(a)})).always((function(){n.removeClass("awsm-b-jobs-loading"),i=!0})))}function l(e){var s=!1;return e.length>0&&e.find(".awsm-b-filter-option").each((function(){a(this).val().length>0&&(s=!0)})),s}function d(a){var t=a.parents(e),n=t.find(".awsm-b-job-search").val();if(t.find(s).data("search",n),0===n.length&&t.find(".awsm-b-job-search-icon-wrapper").addClass("awsm-b-job-hide"),c(t,"jq",n),awsmJobsPublic.deep_linking.search){var i=t.find('input[name="awsm_pagination_base"]');m("jq",n,i.val())}r(t)}a(e).length>0&&a(e).each((function(){var e=a(this),s=e.find(t+" form");(awsmJobsPublic.is_search.length>0||l(s))&&(i=!0,r(e))}));var m=function(a,e,s){s=(s=void 0!==s?s:n).split("?")[0];var t=new URLSearchParams(document.location.search);t.has("paged")&&t.delete("paged"),e.length>0?t.set(a,e):t.delete(a);var i=t.toString();i.length>0&&(i="?"+i),window.history.replaceState({},"",s+i)},c=function(a,e,s){var t=a.find('input[name="awsm_pagination_base"]');if(t.length>0){var n=t.val().split("?"),i="";n.length>1&&(i=n[1]);var o=new URLSearchParams(i);s.length>0?o.set(e,s):o.delete(e),t.val(n[0]+"?"+o.toString()),a.find('input[name="paged"]').val(1)}};function p(){a(".awsm-b-filter-wrap").not(".awsm-b-no-search-filter-wrap").each((function(){var e=a(this),s=e.find(".awsm-b-filter-item").first().offset().top,t=e.find(".awsm-b-filter-item").last().offset().top;window.innerWidth<768?e.removeClass("awsm-b-full-width-search-filter-wrap"):t>s&&e.addClass("awsm-b-full-width-search-filter-wrap")}))}a(t+" .awsm-b-filter-option").on("change",(function(s){s.preventDefault();var t=a(this),n=t.find("option:selected"),i=t.parents(e),o=t.parents(".awsm-b-filter-item").data("filter"),l=n.data("slug");if(c(i,o,l=void 0!==l?l:""),awsmJobsPublic.deep_linking.spec){var d=i.find('input[name="awsm_pagination_base"]');m(o,l,d.val())}r(i)})),a(t+" .awsm-b-job-search-btn").on("click",(function(){d(a(this))})),a(t+" .awsm-b-job-search-close-btn").on("click",(function(){var s=a(this);s.parents(e).find(".awsm-b-job-search").val(""),d(s)})),a(t+" .awsm-b-job-search").on("keypress",(function(e){13==e.which&&(e.preventDefault(),d(a(this)))})),a(s).on("click",".awsm-b-jobs-pagination .awsm-b-load-more-btn, .awsm-b-jobs-pagination a.page-numbers",(function(n){n.preventDefault();var i=a(this),r=i.hasClass("awsm-b-load-more-btn"),d=1,c=[],p=i.parents(e),b=p.find(s),u=i.parents(".awsm-b-jobs-pagination"),h=b.data("listings"),w=b.data("specs"),f=b.data("lang"),g=b.data("search"),v=b.data("awsm-layout"),j=b.data("awsm-hide-expired-jobs"),_=b.data("awsm-other-options");r?(i.prop("disabled",!0),d=void 0===(d=i.data("page"))?1:d):(i.parents(".page-numbers").find(".page-numbers").removeClass("current").removeAttr("aria-current"),i.addClass("current").attr("aria-current","page")),u.addClass("awsm-b-jobs-pagination-loading");var y=p.find(t+" form");if(l(y)&&(c=y.find(".awsm-b-filter-option").serializeArray()),!r){var C=i.attr("href"),x=C.split("?"),k="";if(x.length>1){var P=new URLSearchParams(x[1]);d=P.get("paged"),P.delete("paged"),P.toString().length>0&&(k="?"+P.toString())}C=x[0]+k,c.push({name:"awsm_pagination_base",value:x[0]+k}),awsmJobsPublic.deep_linking.pagination&&m("paged",d,C)}if(awsmJobsPublic.is_tax_archive){var S=b.data("taxonomy"),J=b.data("termId");void 0!==S&&void 0!==J&&c.push({name:"awsm_job_spec["+S+"]",value:J})}c.push({name:"action",value:"block_loadmore"},{name:"paged",value:d}),void 0!==h&&c.push({name:"listings_per_page",value:h}),void 0!==w&&c.push({name:"shortcode_specs",value:w}),void 0!==v&&c.push({name:"awsm-layout",value:v}),void 0!==j&&c.push({name:"awsm-hide-expired-jobs",value:j}),void 0!==_&&c.push({name:"awsm-other-options",value:_}),void 0!==f&&c.push({name:"lang",value:f}),void 0!==g&&c.push({name:"jq",value:g}),a(document).trigger("awsmjobs_block_load_more",[b,c]);var D=o(b);D.length>0&&(c=c.concat(D)),a.ajax({url:awsmJobsPublic.ajaxurl,data:a.param(c),type:"POST",beforeSend:function(){r?i.text(awsmJobsPublic.i18n.loading_text):b.addClass("awsm-b-jobs-loading")}}).done((function(e){if(e){var s=u.data("effectDuration");u.remove(),r?b.append(e):(b.html(e),b.removeClass("awsm-b-jobs-loading"),void 0!==s&&(s=isNaN(s)?s:Number(s),a("html, body").animate({scrollTop:p.offset().top-25},s)))}else i.remove();a(document).trigger("awsmjobs_load_more",[i,e])})).fail((function(a){console.log(a)}))})),a(document).on("click",".awsm-b-filter-toggle",(function(e){e.preventDefault();var s=a(this);s.toggleClass("awsm-on"),s.hasClass("awsm-on")?s.attr("aria-pressed","true"):s.attr("aria-pressed","false"),s.parent().find(".awsm-b-filter-items").slideToggle()})),a(".awsm-b-filter-wrap").not(".awsm-b-no-search-filter-wrap").length>0&&(p(),a(window).on("resize",p))}))}();