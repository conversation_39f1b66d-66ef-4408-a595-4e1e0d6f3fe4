html[dir='rtl'] .awsm-row{
    flex-direction: row-reverse;
}
html[dir='rtl'] .awsm-grid-item{
    float: right;
}
html[dir='rtl'] .awsm-job-item h2.awsm-job-post-title{
    text-align: right;
}
html[dir='rtl'] .awsm-jobs-pagination{
    float: right;
}
html[dir='rtl'] .awsm-list-left-col{
    float: right;
}
html[dir='rtl'] .awsm-list-right-col{
    float: left;
    text-align: left;
}
html[dir='rtl'] .awsm-list-item .awsm-job-specification-item{
    margin-right: 0;
    margin-left: 15px;
}
html[dir='rtl'] .awsm-job-specification-item > [class^="awsm-job-icon-"]{
    margin-right: 0;
    margin-left: 6px;
    float: right;
}
html[dir='rtl'] .awsm-job-more-container .awsm-job-more span::before{
    content: "\002190";
}
html[dir='rtl'] .awsm-job-content .awsm-job-specification-label{
    float: right;
    margin-left: 4px;
}


html[dir='rtl'] .awsm-job-single-wrap.awsm-col-2 .awsm-job-content{
    float: right;
    padding-right: 0;
    padding-left: 15px;
}
html[dir='rtl'] .awsm-job-single-wrap.awsm-col-2 .awsm-job-form {
    float: right;
    padding-right: 15px;
    padding-left: 0;
}
html[dir='rtl'] .awsm-job-list-info span{
    margin-right: 0;
    margin-left: 10px;
}
html[dir='rtl'] .awsm-job-form-group input[type=checkbox],
html[dir='rtl'] .awsm-job-form-group input[type=radio]{
    margin-right: 0;
    margin-left: 5px;
}

html[dir='rtl'] ul.awsm-error-message li{
    margin-left: 0;
    margin-right: 1.2em;
}

html[dir='rtl'] .awsm-selectric .label{
    margin: 0 10px 0 48px;
    text-align: right;
}
html[dir='rtl'] .awsm-selectric .awsm-selectric-arrow-drop{
    left: 0;
    right: auto;
}

@media (max-width:992px){
    html[dir='rtl'] .awsm-job-single-wrap.awsm-col-2 .awsm-job-content {
        padding-left: 0;
    }
    html[dir='rtl'] .awsm-job-single-wrap.awsm-col-2 .awsm-job-form {
        padding-left: 0;
    }
}
@media (max-width:768px){
    html[dir='rtl'] .awsm-list-right-col{
        text-align: right;
    }
}
