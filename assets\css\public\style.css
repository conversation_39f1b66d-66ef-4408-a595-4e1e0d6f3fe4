.awsm-row {
	margin: 0 -15px;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	display: flexbox;
	-webkit-flex-flow: row wrap;
	flex-flow: row wrap;
}

.awsm-row,
.awsm-row *,
.awsm-row *::before,
.awsm-row *::after {
	box-sizing: border-box;
}

.awsm-grid-item {
	float: left;
	width: 33.333%;
	padding: 0 15px !important;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	-webkit-flex-direction: column;
}

.awsm-grid-col-4 .awsm-grid-item {
	width: 25%;
}

.awsm-grid-col-2 .awsm-grid-item {
	width: 50%;
}

.awsm-grid-col .awsm-grid-item {
	width: 100%;
}

.awsm-job-hide {
	display: none !important;
}

.awsm-job-show {
	display: block !important;
}

.awsm-job-item {
	background: #fff;
	padding: 20px;
	font-size: 14px;
}

a.awsm-job-item {
	text-decoration: none !important;
}

.awsm-grid-item .awsm-job-item {
	margin-bottom: 30px;
	box-shadow: 0 1px 4px 0 rgb(0 0 0 / 5%);
	border: 1px solid #dddfe3;
	border-radius: 2px;
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	-webkit-flex-direction: column;
	flex-grow: 1;
	-webkit-flex-grow: 1;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.awsm-grid-item .awsm-job-item:hover,
.awsm-grid-item .awsm-job-item:focus {
	box-shadow: 0 3px 15px -5px rgb(0 0 0 / 20%);
}

.awsm-grid-item .awsm-job-featured-image {
	margin-bottom: 14px;
}

.awsm-job-item h2.awsm-job-post-title {
	margin: 0 0 15px;
	font-size: 18px;
	text-align: left;
}

.awsm-job-item h2.awsm-job-post-title a {
	font-size: 18px;
}

.awsm-grid-item .awsm-job-info {
	min-height: 83px;
	margin-bottom: 10px;
}

.awsm-grid-item .awsm-job-info p {
	margin: 0 0 8px;
}

.awsm-job-wrap::after {
	clear: both;
	content: '';
	display: table;
}
.awsm-filter-wrap,
.awsm-filter-wrap * {
	box-sizing: border-box;
}

.awsm-job-wrap, .awsm-job-wrap * {
	box-sizing: border-box;
  }
  
.awsm-filter-wrap {
	margin: 0 -10px 20px;
}
.awsm-filter-wrap form{
	display: flex;
	flex-wrap: wrap;
}
.awsm-filter-wrap .awsm-filter-items{
	display: none;
}
.awsm-filter-wrap.awsm-full-width-search-filter-wrap .awsm-filter-item-search {
	width: 100%;
}
.awsm-filter-wrap.awsm-full-width-search-filter-wrap .awsm-filter-items,
.awsm-filter-wrap.awsm-no-search-filter-wrap .awsm-filter-items {
	width: 100%;
}
.awsm-filter-toggle{
	display: flex;
	flex-flow: wrap;
	width: 46px;
	padding: 12px;
	border: 1px solid #ccc;
	margin: 0 10px 10px;
	border-radius: 4px;
	outline: none !important;
}
.awsm-filter-toggle.awsm-on{
	background: #ccc;
}
.awsm-filter-toggle svg{
	width: 20px;
	height: 20px;
}
.awsm-filter-wrap.awsm-no-search-filter-wrap .awsm-filter-toggle {
	width: 100%;
	align-items: center;
	justify-content: space-between;
	text-decoration: none;
}
.awsm-filter-wrap.awsm-no-search-filter-wrap .awsm-filter-toggle svg {
	width: 22px;
	height: 22px;
}
@media (min-width:768px){
	.awsm-filter-wrap .awsm-filter-items{
		display: flex !important;
		flex-wrap: wrap;
		width: calc(100% - 250px);
	}
	.awsm-filter-toggle{
		display: none;
	}
	.awsm-filter-item-search{
		width: 250px;
	}
}
.awsm-filter-wrap .awsm-filter-item {
	/* display: inline-block; */
	padding: 0 10px 10px;
	/* vertical-align: top; */
}

.awsm-filter-item-search{
	position: relative;
	padding: 0 10px 10px;
}
@media (max-width:768px){
	.awsm-filter-wrap .awsm-filter-items{
		width: 100%;
	}
	.awsm-filter-item-search{
		width: calc(100% - 66px);
	}
	.awsm-filter-wrap .awsm-filter-item .awsm-selectric-wrapper{
		min-width: 100%;
	}
}
.awsm-filter-wrap .awsm-filter-item .awsm-job-form-control{
	min-height: 48px;
	padding-right: 58px;
}
.awsm-filter-item-search .awsm-job-form-control {
    padding-right: 48px;
    min-height: 48px;
}
.awsm-filter-item-search-in {
	position: relative;
}
.awsm-filter-item-search .awsm-job-search-icon-wrapper {
	position: absolute;
	right: 0;
	top: 0;
	width: 48px;
	height: 100%;
	font-size: 16px;
	color: #ccc;
	line-height: 48px;
	text-align: center;
	cursor: pointer;
}
.awsm-jobs-none-container {
	padding: 25px;
}

.awsm-jobs-none-container p {
	margin: 0;
	padding: 5px;
}

.awsm-row .awsm-jobs-pagination {
	padding: 0 15px;
	width: 100%;
}

.awsm-jobs-pagination {
	float: left;
	width: 100%;
}

.awsm-load-more-main a.awsm-load-more, .awsm-load-more-classic a.page-numbers	 {
	display: block;
	text-align: center;
	padding: 20px;
	background: #fff;
	box-shadow: 0 1px 4px 0 rgb(0 0 0 / 5%);
	border: 1px solid #dddfe3;
	margin: 0 !important;
	text-decoration: none !important;
	outline: none !important;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}
.awsm-load-more-classic a.page-numbers, .awsm-load-more-classic span.page-numbers {
	padding: 5px 10px;
	font-size: 90%;
}
.awsm-load-more-classic {
	text-align: center;
}

.awsm-load-more-main a.awsm-load-more:hover,
.awsm-load-more-main a.awsm-load-more:focus,
.awsm-load-more-classic a.page-numbers:hover,
.awsm-load-more-classic a.page-numbers:focus {
	box-shadow: 0 3px 15px -5px rgb(0 0 0 / 20%);
}

.awsm-jobs-pagination.awsm-load-more-classic ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.awsm-jobs-pagination.awsm-load-more-classic ul li {
    display: inline-block;
}

/*---- List ----*/

.awsm-lists {
	border: 1px solid #ededed;
}

.awsm-list-item {
	width: 100%;
}

.awsm-list-item h2.awsm-job-post-title {
	margin-bottom: 0;
}

.awsm-list-item .awsm-job-featured-image {
	float: left;
    margin-right: 10px;
}

.awsm-list-item .awsm-job-featured-image img {
	width: 50px;
    height: 50px;
}

.awsm-list-item .awsm-job-item {
	border-bottom: 1px solid rgba(0, 0, 0, 0.13);
}

.awsm-list-item .awsm-job-item::after {
	content: "";
	display: table;
	clear: both;
}

.awsm-list-left-col {
	float: left;
	width: 50%;
}

.awsm-list-right-col {
	float: left;
	width: 50%;
	text-align: right;
}

.awsm-list-item .awsm-job-specification-wrapper {
	display: inline-block;
	vertical-align: middle;
}

.awsm-list-item .awsm-job-specification-item {
	display: inline-block;
	vertical-align: middle;
	margin: 0 15px 0 0;
}

a.awsm-job-item .awsm-job-specification-item {
	color: #4C4C4C;
}

.awsm-list-item .awsm-job-more-container {
	display: inline-block;
	vertical-align: middle;
}

.awsm-job-more-container .awsm-job-more span::before {
	content: "\002192";
}

.awsm-lists .awsm-jobs-pagination {
	margin-top: 30px;
}

.awsm-job-specification-item>[class^="awsm-job-icon-"] {
	margin-right: 6px;
}

.awsm-job-specification-term::after {
	content: ", ";
}

.awsm-job-specification-term:last-child::after {
	content: "";
}

/*----- Single ----*/

.awsm-job-single-wrap,
.awsm-job-single-wrap *,
.awsm-job-single-wrap *::before,
.awsm-job-single-wrap *::after {
	box-sizing: border-box;
}

.awsm-job-single-wrap {
	margin-bottom: 1.3em;
}

.awsm-job-single-wrap::after {
	content: "";
	display: table;
	clear: both;
}

.awsm-job-content {
	padding-bottom: 32px;
}

.awsm-job-single-wrap.awsm-col-2 .awsm-job-content {
	float: left;
	width: 55%;
	padding-right: 15px;
}

.awsm-job-single-wrap.awsm-col-2 .awsm-job-form {
	float: left;
	width: 45%;
	padding-left: 15px;
}

.awsm-job-head,
.awsm_job_spec_above_content {
	margin-bottom: 20px;
}

.awsm-job-head h1 {
	margin: 0 0 20px;
}

.awsm-job-list-info span {
	margin-right: 10px;
}

.awsm-job-single-wrap .awsm-job-expiration-label {
	font-weight: bold;
}

.awsm-job-form-inner {
	background: #fff;
	border: 1px solid #dddfe3;
	padding: 35px;
}

.awsm-job-form-inner h2 {
	margin: 0 0 30px;
}

.awsm-job-form-group {
	margin-bottom: 20px;
}

.awsm-job-form-group input[type=checkbox],
.awsm-job-form-group input[type=radio] {
	margin-right: 5px;
}

.awsm-job-form-group label {
	display: block;
	margin-bottom: 10px;
}

.awsm-job-inline-group label,
.awsm-job-form-options-container label {
	display: inline;
	font-weight: normal;
}

.awsm-job-form-control {
	display: block;
	width: 100%;
}

.awsm-job-form-options-container span {
	display: inline-block;
	margin-bottom: 10px;
	margin-left: 10px;
}

.awsm-job-submit {
	background: #0195ff;
	border: 1px solid #0195ff;
	padding: 10px 30px;
	color: #fff;
}

.awsm-job-submit:hover,
.awsm-job-submit:focus {
	background: rgba(0, 0, 0, 0);
	color: #0195ff;
}

.awsm-job-form-error {
	color: #db4c4c;
	font-weight: 500;
}

.awsm-job-form-control.awsm-job-form-error,
.awsm-job-form-control.awsm-job-form-error:focus {
	border: 1px solid #db4c4c;
}

.awsm-success-message,
.awsm-error-message {
	padding: 12px 25px;
}

.awsm-success-message p:empty,
.awsm-error-message p:empty {
	display: none;
}

.awsm-success-message p,
.awsm-error-message p {
	margin: 0 !important;
	padding: 0 !important;
}

.awsm-success-message {
	border: 1px solid #1ea508;
}

.awsm-error-message {
	border: 1px solid #db4c4c;
}

ul.awsm-error-message li {
	margin-left: 1.2em;
	line-height: 1.8em;
}

.awsm-expired-message {
	padding: 25px;
}

.awsm-expired-message p {
	margin: 1em 0em;
}

.awsm-job-container {
	max-width: 1170px;
	width: 100%;
	margin: 0 auto;
	padding: 50px 0;
}

.awsm-jobs-loading {
	position: relative;
}

.awsm-job-listings::after {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: rgba(255, 255, 255, 0.5) url(../img/loading.svg) no-repeat center;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.awsm-job-listings.awsm-jobs-loading::after {
	opacity: 1;
	visibility: visible;
}

/*---- Accessibility ----*/

.awsm-sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0,0,0,0);
	border: 0;
}

/*---- Media Queries ----*/

@media (max-width:1024px) {
	.awsm-grid-col-4 .awsm-grid-item {
		width: 33.333%;
	}
}

@media (max-width:992px) {
	.awsm-job-single-wrap.awsm-col-2 .awsm-job-content {
		width: 100%;
		padding-right: 0;
	}

	.awsm-job-single-wrap.awsm-col-2 .awsm-job-form {
		width: 100%;
		padding-left: 0;
	}
}

@media (max-width:768px) {

	.awsm-grid-col-4 .awsm-grid-item,
	.awsm-grid-col-3 .awsm-grid-item,
	.awsm-grid-item {
		width: 50%;
	}

	.awsm-list-left-col {
		width: 100%;
		padding-bottom: 10px;
	}

	.awsm-list-right-col {
		width: 100%;
		text-align: left;
	}
}

@media (max-width:648px) {

	.awsm-grid-col-4 .awsm-grid-item,
	.awsm-grid-col-3 .awsm-grid-item,
	.awsm-grid-col-2 .awsm-grid-item,
	.awsm-grid-item {
		width: 100%;
	}

	.awsm-list-item .awsm-job-specification-wrapper {
		display: block;
		padding-bottom: 5px;
		float: none;
	}

	.awsm-list-item .awsm-job-more-container {
		display: block;
		float: none;
	}
}

.awsm-job-form-plugin-style .awsm-job-form-control{
	display: block;
	width: 100%;
	font: inherit;
	padding: 8px 15px;
	min-height: 46px;
	border: 1px solid #ccc;
	border-radius: 4px;
	line-height: 1;
	color: #060606;
	transition:  all 0.3s ease;
}
.awsm-job-form-plugin-style .awsm-job-form-control:focus{
	outline: none;
	box-shadow: none;
	border-color: #060606;
}
.awsm-job-form-plugin-style .awsm-job-form-control.awsm-job-form-error{
	border-color: #db4c4c;
}
.awsm-job-form-plugin-style textarea.awsm-job-form-control{
	min-height: 80px;
}
.awsm-job-form-plugin-style .awsm-jobs-primary-button,
.awsm-job-form-plugin-style .awsm-application-submit-btn {
	background: #060606;
	border-radius: 45px;
	transition:  all 0.3s ease;
	padding: 16px 32px;
	color: #fff;
}
.awsm-job-form-plugin-style .awsm-jobs-primary-button:hover,
.awsm-job-form-plugin-style .awsm-jobs-primary-button:focus,
.awsm-job-form-plugin-style .awsm-application-submit-btn:hover,
.awsm-job-form-plugin-style .awsm-application-submit-btn:focus{
	color: #fff;
    outline: none;
    background: #060606;
}
.awsm-job-form-plugin-style .awsm-jobs-primary-button {
	cursor: pointer;
}
.awsm-job-form-plugin-style .awsm-jobs-primary-button:disabled {
	opacity: 0.5;
	pointer-events: none;
}
.awsm-job-form-plugin-style .awsm-selectric{
	border-color: #ccc;
	box-shadow: none;
	border-radius: 4px;
}
.awsm-job-form-plugin-style .awsm-selectric-open .awsm-selectric{
	border-color: #060606;
}
.awsm-job-form-plugin-style .awsm-selectric .label{
	margin-left: 15px;
}

/* Block Theme - Compatibility Templates Styles */

.awsm-jobs-is-block-theme .site-branding {
	padding: 0 2.1rem;
}

.awsm-jobs-is-block-theme .site-content {
	padding: 0 2.1rem 3rem;
}

.awsm-jobs-is-block-theme .site-title {
	margin-bottom: 0;
}

.awsm-job-featured-image img {
    max-width: 100%;
    height: auto;
}

